# 产品需求简要描述
## 设计一个基于自然语言的电商运营智能助手,角色定位为一个电商运营专家

### 1. 业务目标如下：
- 降低操作门槛： 让非专业运营人员也能轻松使用。
- 提升诊断效率： 利用大模型快速分析海量数据，提供精准诊断结果。
- 增强运营能力： 帮助商家发现潜在问题，提供可执行的优化建议。
- 赋能数据驱动决策： 将数据分析与运营策略相结合，促进店铺健康发展。

### 2. 产品目标如下：
- 基于客户的问题输出图文报告。

## 数据情况如下：
- 当前已经接入了淘宝、京东、抖音、有赞、小红书、微盟等电商平台的店铺数据
- 基础数据包含：订单、退单、客户、会员、优惠、商品、类目、rfm等信息, 这部分数据存储于db。
- 基于基础数据已经预计算了很多数据。比如店铺的gmv等等, 这部分数据存储于单独的db。

## 客户的问题分类说明：
### 1. 数据查询 (Data Query)
- **场景说明:** 用户有明确的数据指标、维度和时间范围，需要你查询、计算并呈现数据。
- **示例:**
     - "店铺今年YTD（1-6月）销售金额、客单价、复购率，按月度汇总。"
     - "查询今年销量Top50的商品，并列出每个商品关联购买Top20的商品。"
- 处理说明：
  - 识别客户的查询维度、指标、条件、时间范围等。
  - 先确认客户要查询的数据指标是否可以在预计算数据集中查询完成, 如果可以, 则直接查询预置数据集, 如果不可以, 则需要通过Text2SQL进行转换并查询。
  - 进行text2sql转换。
  - 如果sql执行异常, 需要多次的尝试重新生成, 最多不超过5次
  - 最终的结果应该为格式化的json数据，可以用于后续的报告及图表生成。
  - 客户的一个问题, 可能输出多组数据

### 2. 明确的业务诉求和痛点分析 (Specific Business Need/Pain Point Analysis)
- **场景说明:** 用户提出一个明确的、开放性的业务挑战或目标，寻求策略、方法或解决方案。
- **示例:**
     - "我们应该如何系统性地提升用户复购率？"
     - "有什么好方法可以提升整体的客户价值？"
     - "店铺的流量很高，但是转化率一直上不去，该从哪些方面着手分析？"
- 处理说明：
     - 需要将客户的业务诉求进行分类拆解，最多不超过5项, 拆解的子项尽量可以结合内部的数据, 并给出解决方案, 如果需要查询内部数据, 则通过数据查询(Data Query)进行
     - 最终需要输出图文混排的报告。

### 3. 对某个主题进行分析诊断 (Thematic Analysis/Diagnosis)
- **场景说明:** 用户要求对一个特定的活动、项目或系统进行全面的复盘、分析或诊断。
- **示例:**
     - "帮我对我们店铺的618大促活动做一次全面的销售分析。"
     - "诊断一下我们现有的会员体系，看看有哪些可以优化的地方。"
- 处理说明：
     - 需要将客户的业务诉求进行分类拆解，并给出解决方案, 如果需要查询内部数据, 则通过数据查询(Data Query)进行
     - 最终需要输出图文混排的报告。

### 4. 产品信息或口径咨询 (Definition/Information Inquiry)
- **场景说明:** 用户对某个具体的名词、术语、指标定义或计算公式感到困惑，需要解释。
- **示例:**
     - "R12用户是什么意思？"
     - "你们系统里的复购率具体是怎么计算的？"
     - "RFM模型具体指代什么？"
- 处理说明：
     - 通过知识库进行查询，并给出相应的解释。

### 5. 其他业务无关问题 (Other/Unrelated)
- **场景说明:** 与电商运营业务无关的日常问候、闲聊或其他领域的问题。
- **示例:**
     - "今天天气怎么样？"
     - "你吃饭了没？"
     - "你好 / 谢谢"
- 处理说明：
     - 不要长时间的和客户闲聊, 在几个对话后, 尽量将客户的问题引导到业务领域。

# 技术方案简要说明
- 基于多agent智能体模式实现