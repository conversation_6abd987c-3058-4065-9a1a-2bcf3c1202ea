# 背景说明 
我是一个商家服务商, 我的客户是电商领域的商家, 客户授权我获取了客户的多个渠道(淘宝、京东、抖音、有赞、小红书、微盟等)的数据(订单、退单、客户、会员等等), 存储在ADB4mysql数据库。我主要通过这些数据来帮助客户进行店铺运营，进行营销活动等等。

# 需求描述
现在我需要给客户提供一个基于自然语言交互的智能运营系统, 请帮我设计完整的架构。

## 产品要求：
- 客户通过自然语言进行系统对话，支持客户上传外部的文件，提供辅助的信息(外部文件格式限制在txt, csv, excel, 大小最大为1M)。
- 需要良好的理解客户的对话上下文, 保证客户对话的连续性。
- 客户的需求会分为几类
  + 数据指标的查询(示例场景：场景来源1：周报、月报、日常监控需求;场景来源2：讨论分析某个业务问题，需要临时查看特定数据做验证) 
  + 业务分析与诊断(比如如何提升客单价、如何提升客户价值等), 客户有明确业务诉求，结合实际数据和专业知识给做出分析与建议。
  + 对某个主题进行分析诊断。客户没有明确业务诉求，需要通过分析诊断。示例场景(对店铺618大促进行销售分析; 会员体系诊断)
  + 信息咨询类的。主要是针对业务口径、数据口径之类的咨询。
  + 业务无关的问题。比如(今天天气咋样啊, 吃饭了没)。这种可以结合通用知识和客户沟通,并给出合理的答案。当实在不知道的时候，引导客户到系统的能力问题。
- 需要结合客户店铺的公开信息，比如所属的行业，店铺的规模，店铺的运营模式，店铺的定位等等。
- 需要结合外置的rag知识库，包含术语、口径等信息。
- 对于数据查询问题务必通过Text2SQL进行转换。
- 当客户的问题描述不清晰的时候，请合理的进行追问或者澄清。
- 最终输出给客户的是完整的运营报告，包含图表、文本等等。
- 数据库的结构参考：template.sql。系统分租户进行存储, 表名中的租户变量在生成sql的时候务必需要替换。


## 技术架构要求
- 后端：springboot。jdk1.8
- 大模型：deepseek。
- 技术实现：基于ai agent实现。请务必良好的拆分agent,并确保agent之间相互独立,并且agent可以良好的协作。务必精心设计每个agent的提示词
- 数据库：ADB4mysql。
- 前端：vue
- 提供演示的前端页面, 页面风格简洁现代。
- 对话以sse流式方式进行。

## 其他要求
实现后要仔细检查，并确保系统可以正确运行。