-- 主订单表
CREATE TABLE IF NOT EXISTS `ccms_order_${tenantId}` (
  `uni_order_id` varchar(100) NOT NULL COMMENT '全渠道订单ID',
  `tenant` varchar(50) NOT NULL COMMENT '租户id',
  `data_from` tinyint(1) DEFAULT NULL COMMENT '数据来源0：ETL 1：标准API 2：用户导入',
  `partner` varchar(50) NOT NULL COMMENT '合作伙伴',
  `plat_code` varchar(100) NOT NULL COMMENT '平台代码',
  `order_id` varchar(100) NOT NULL COMMENT '原始主订单ID',
  `uni_shop_id` varchar(100) NOT NULL COMMENT '全渠道店铺ID',
  `uni_id` varchar(100) NOT NULL COMMENT '全渠道渠道客户id',
  `guide_id` varchar(100) DEFAULT NULL COMMENT '导购员id',
  `shop_id` varchar(100) NOT NULL COMMENT '原始店铺ID',
  `plat_account` varchar(350) DEFAULT NULL COMMENT '原始平台帐号',
  `total_fee` decimal(13,2) DEFAULT NULL COMMENT '订单优惠前总金额 ',
  `item_discount_fee` decimal(13,2) DEFAULT NULL COMMENT '商品级优惠总金额',
  `trade_discount_fee` decimal(13,2) DEFAULT NULL COMMENT '订单级优惠金额',
  `adjust_fee` decimal(13,2) DEFAULT NULL COMMENT '手工调整优惠金额',
  `post_fee` decimal(10,2) DEFAULT NULL COMMENT '邮费金额',
  `discount_rate` decimal(10,2) DEFAULT NULL COMMENT '主订单整单折扣率',
  `payment_no_postfee` decimal(13,2) DEFAULT NULL COMMENT '不含邮费的应付金额',
  `payment` decimal(13,2) DEFAULT NULL COMMENT '应付金额',
  `pay_time` datetime DEFAULT NULL COMMENT '付款成功时间',
  `product_num` int(11) DEFAULT NULL COMMENT '订单商品总数',
  `order_status` varchar(50) DEFAULT NULL COMMENT '订单状态,下单未支付（待付款）WAIT_BUYER_PAY 已付款未发货 WAIT_SELLER_SEND_GOODS 已付款，卖家部分发货 SELLER_CONSIGNED_PART 已发货未付款（用于货到付款）WAIT_BUYER_CONFIRM_PAY (买家已签收,货到付款专用) TRADE_BUYER_SIGNED 已发货未确认（待收货）WAIT_BUYER_CONFIRM_GOODS 交易成功 （由买家确认收货或系统自动触发）TRADE_FINISHED 交易关闭（付款以前，卖家或买家主动关闭交易，包括平台自动关闭情况）TRADE_CLOSED PAID_FORBID_CONSIGN，该状态代表订单已付款但是处于禁止发货状态（例如线上下单线下门店发货）。注：部分退款，退款成功，订单状态为交易成功 部分退款，退款失败，订单状态为交易成功 全部退款，退款成功，订单状态为交易关闭 全部退款，退款失败，订单状态为交易成功 针对预付业务，付完尾款才是待发货状态，未付尾款且尾款未超时状态为待付款 预付业务中尾款支付超时则状态为交易关闭 ',
  `is_refund` varchar(50) DEFAULT 'SY_REFUND_NONE' COMMENT '是否退单,无退单（SY_REFUND_NONE）全部退单（SY_REFUND_ALL）部分退单 （SY_REFUND_PART）默认：SY_REFUND_NONE',
  `refund_fee` decimal(10,2) DEFAULT NULL COMMENT '退款金额',
  `step_paid_fee` decimal(13,2) DEFAULT NULL COMMENT '预售订单实付金额',
  `is_used_store_card` tinyint(1) DEFAULT '0' COMMENT '订单是否使用了存储卡;0-未使用，1-使用',
  `store_card_used` decimal(13,2) DEFAULT NULL COMMENT '本次订单使用的购物金',
  `store_card_basic_used` decimal(13,2) DEFAULT NULL COMMENT '本次订单使用的购物金本金',
  `store_card_expand_used` decimal(13,2) DEFAULT NULL COMMENT '本次订单使用的购物金权益金',
  `order_promotion_num` int(11) DEFAULT '0' COMMENT '主订单优惠笔数',
  `item_promotion_num` int(11) DEFAULT '0' COMMENT '子订单优惠笔数之和',
  `created` datetime DEFAULT NULL COMMENT '平台下单时间',
  `endtime` datetime DEFAULT NULL COMMENT '订单结束时间',
  `modified` datetime DEFAULT NULL COMMENT '订单的修改时间',
  `trade_type` varchar(50) DEFAULT NULL COMMENT '交易类型,支持的交易类型：普通订单（一口价） FIXED 团购订单 (包括预售订单) STEP 无付款订单(包括积分兑礼、抽奖赠送) NO_PAID 货到付款 COD 电子凭证订单（虚拟物品订单） E_TICKET 线下门店订单 OFFLINE_TRADE',
  `receiver_name` varchar(700) DEFAULT NULL COMMENT '收货人姓名',
  `receiver_country` varchar(50) DEFAULT NULL COMMENT '收货人国家',
  `receiver_state` varchar(50) DEFAULT NULL COMMENT '收货人省份',
  `receiver_city` varchar(50) DEFAULT NULL COMMENT '收货人市',
  `receiver_district` varchar(50) DEFAULT NULL COMMENT '收货人区域',
  `receiver_town` varchar(50) DEFAULT NULL COMMENT '收货人街道（乡镇）',
  `receiver_address` varchar(3000) DEFAULT NULL COMMENT '收货人地址',
  `receiver_mobile` varchar(400) DEFAULT NULL COMMENT '收货人手机号',
  `trade_source` varchar(50) DEFAULT NULL COMMENT '交易来源',
  `delivery_type` varchar(50) DEFAULT NULL COMMENT '物流方式,1:快递发货 (SY_EXPRESS) 2:店内自提（SY_SELFLIFT） 3:无需物流（SY_NONE）4:同城配送(SY_INTRA_CITY_SERVICE)',
  `consign_time` datetime DEFAULT NULL COMMENT '发货时间',
  `buyer_remark` text DEFAULT NULL COMMENT '买家备注',
  `seller_remark` text DEFAULT NULL COMMENT '卖家备注',
  `seller_flag` varchar(50) DEFAULT NULL COMMENT '卖家旗帜',
  `orders_num` int(11) DEFAULT 0 COMMENT '子订单数量',
  `is_presale` tinyint(1) DEFAULT NULL COMMENT '是否为预售订单',
  `presale_status` varchar(32) DEFAULT NULL COMMENT '预售订单状态',
  `first_fee_paytime` datetime DEFAULT NULL COMMENT '付首款的时间',
  `last_fee_paytime` datetime DEFAULT NULL COMMENT '付尾款的时间',
  `first_paid_fee` decimal(10,2) DEFAULT NULL COMMENT '已付定金',
  `insert_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '第一次插入数据库时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据库更新时间',
  `plat_data` text DEFAULT NULL COMMENT '平台个性化字段,json',
  `trade_business_type` varchar(16) COMMENT '交易业务类型',
  `rx_audit_status` tinyint COMMENT '处方药审核状态',
  `is_part_consign` tinyint COMMENT '是否是多次发货的订单',
  `timeout_action_time` datetime COMMENT '超时到期时间',
  `buyer_is_rate` varchar(50) COMMENT '买家是否已评价',
  `pay_type` varchar(50) COMMENT '支付方式',
	`package_bag_money` decimal(10, 2) DEFAULT '0.00' COMMENT '打包袋金额',
  `remark` varchar(2000) COMMENT '备注',
	`refund_product_num` int COMMENT '退款商品数量',
	`point_use` int COMMENT '商品支付用于抵现的积分数',
  `point_fee` decimal(10, 2) COMMENT '商品支付使用积分抵扣的订单金额',
	`direct_parent_order_id` varchar(100) COMMENT '直接父订单号',
  `parent_order_id` varchar(100) COMMENT '根父订单号',
  PRIMARY KEY (`uni_order_id`)
) DISTRIBUTED BY HASH(`uni_order_id`) INDEX_ALL='Y'  block_size=4096  COMMENT='主订单信息';

-- 主订单优惠
CREATE TABLE IF NOT EXISTS `ccms_order_promotion_${tenantId}` (
  `data_from` tinyint(1) DEFAULT NULL COMMENT '数据来源0：ETL 1：标准API 2：用户导入',
  `partner` varchar(50) NOT NULL COMMENT '合作伙伴',
  `plat_code` varchar(100) NOT NULL COMMENT '平台代码',
  `shop_id` varchar(100) NOT NULL COMMENT '原始店铺ID',
  `uni_shop_id` varchar(100) NOT NULL COMMENT '全渠道店铺ID',
  `order_id` varchar(100) NOT NULL COMMENT '原始主订单ID',
  `uni_order_id` varchar(100) NOT NULL COMMENT '全渠道订单ID',
  `uni_id` varchar(100) NOT NULL COMMENT '全渠道渠道客户id',
  `promotion_type` varchar(50) DEFAULT NULL COMMENT '优惠类型',
  `promotion_id` varchar(256) DEFAULT NULL COMMENT '优惠id',
  `uni_promotion_id` varchar(256) DEFAULT NULL COMMENT '全渠道优惠id',
  `promotion_name` varchar(100) DEFAULT NULL COMMENT '优惠信息的名称',
  `promotion_desc` varchar(100) DEFAULT NULL COMMENT '优惠活动的描述',
  `discount_fee` decimal(13,2) DEFAULT NULL COMMENT '优惠金额',
  `insert_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '第一次插入数据库时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据库更新时间',
  `promotion_class` int(11) COMMENT '优惠类别',
  `promotion_way` int(11) COMMENT '优惠方式',
  PRIMARY KEY (`uni_promotion_id`),
  KEY `idx_uni_order_id` (`uni_order_id`),
  KEY `idx_uni_id` (`uni_id`),
  KEY `idx_discount_fee` (`discount_fee`)
) DISTRIBUTED BY HASH(`uni_promotion_id`) DEFAULT CHARSET=utf8 block_size=4096 COMMENT='订单优惠记录表';

-- 子订单优惠
CREATE TABLE IF NOT EXISTS `ccms_order_item_promotion_${tenantId}` (
  `data_from` tinyint(1) DEFAULT NULL COMMENT '数据来源0：ETL 1：标准API 2：用户导入',
  `partner` varchar(50) NOT NULL COMMENT '合作伙伴',
  `plat_code` varchar(100) NOT NULL COMMENT '平台代码',
  `uni_id` varchar(100) NOT NULL COMMENT '全渠道渠道客户id',
  `uni_shop_id` varchar(100) NOT NULL COMMENT '全渠道店铺ID',
  `shop_id` varchar(100) NOT NULL COMMENT '原始店铺ID',
  `order_id` varchar(100) NOT NULL COMMENT '原始主订单ID',
  `order_item_id` varchar(100) NOT NULL COMMENT '原始子订单ID',
  `uni_order_id` varchar(100) NOT NULL COMMENT '全渠道订单ID',
  `uni_order_item_id` varchar(100) NOT NULL COMMENT '全渠道子订单ID',
  `product_id` varchar(150) DEFAULT NULL COMMENT '商品ID',
  `uni_product_id` varchar(100) DEFAULT NULL COMMENT '全渠道商品ID',
  `sku_id` varchar(150) DEFAULT NULL COMMENT '商品SKU_ID',
  `promotion_type` varchar(50) DEFAULT NULL COMMENT '优惠类型',
  `promotion_id` varchar(150) DEFAULT NULL COMMENT '优惠id',
  `uni_promotion_id` varchar(200) DEFAULT NULL COMMENT '全渠道优惠id',
  `promotion_name` varchar(100) DEFAULT NULL COMMENT '优惠信息的名称',
  `promotion_desc` varchar(100) DEFAULT NULL COMMENT '优惠活动的描述',
  `discount_fee` decimal(13,2) DEFAULT NULL COMMENT '优惠金额',
  `insert_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '第一次插入数据库时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据库更新时间',
  `promotion_class` int(11) COMMENT '优惠类别',
  `promotion_way` int(11) COMMENT '优惠方式',
  PRIMARY KEY (`uni_promotion_id`),
  KEY `idx_uni_order_id` (`uni_order_id`),
  KEY `idx_uni_order_item_id` (`uni_order_item_id`),
  KEY `idx_uni_id` (`uni_id`),
  KEY `idx_discount_fee` (`discount_fee`)
) DISTRIBUTED BY HASH(`uni_promotion_id`) DEFAULT CHARSET=utf8  block_size=4096  COMMENT='子订单优惠记录表';

-- 子订单表
CREATE TABLE IF NOT EXISTS `ccms_order_item_${tenantId}` (
  `uni_order_item_id` varchar(100) NOT NULL COMMENT '全渠道子订单ID',
  `tenant` varchar(50) NOT NULL COMMENT '租户id',
  `data_from` tinyint(4) DEFAULT 0 COMMENT '数据来源,0：ETL 1：标准API 2：用户导入',
  `partner` varchar(50) NOT NULL COMMENT '合作伙伴',
  `uni_order_id` varchar(100) NOT NULL COMMENT '全渠道订单ID',
  `plat_code` varchar(100) NOT NULL COMMENT '平台代码',
  `order_item_id` varchar(100) NOT NULL COMMENT '子订单ID',
  `order_id` varchar(100) NOT NULL COMMENT '主订单ID',
  `uni_shop_id` varchar(100) NOT NULL COMMENT '全渠道店铺ID',
  `uni_id` varchar(100) NOT NULL COMMENT '全渠道客户ID',
  `shop_id` varchar(50) NOT NULL COMMENT '平台店铺ID',
  `plat_account` varchar(350) DEFAULT NULL COMMENT '平台帐号',
  `outer_product_id` varchar(150) DEFAULT NULL COMMENT '外部商品ID',
  `outer_sku_id` varchar(150) DEFAULT NULL COMMENT '外部商品SKU_ID',
  `product_id` varchar(150) DEFAULT NULL COMMENT '商品ID',
  `product_name` varchar(300) DEFAULT NULL COMMENT '商品名称',
  `uni_product_id` varchar(100) DEFAULT NULL COMMENT '全渠道商品ID',
  `sku_id` varchar(150) DEFAULT NULL COMMENT '商品SKU_ID',
  `sku_properties_name` varchar(1000) DEFAULT NULL COMMENT '商品sku规格',
  `product_num` int(11) DEFAULT NULL COMMENT '商品数量',
  `price` decimal(13,2) DEFAULT NULL COMMENT '商品原价',
  `discount_fee` decimal(13,2) DEFAULT NULL COMMENT '折扣金额',
  `adjust_fee` decimal(13,2) DEFAULT NULL COMMENT '子订单调整金额',
  `total_fee` decimal(13,2) DEFAULT NULL COMMENT '子订单优惠后的总金额',
  `payment` decimal(13,2) DEFAULT NULL COMMENT '应付金额 (子订单平摊后的金额) 主订单优惠金额 = 主订单优惠 + 主订单调整金额 子订单平摊主订单优惠金额 = 主订单优惠金额 * 子订单优惠后的总金额的占比 子订单平摊后的金额= 子订单优惠后的总金额- 子订单平摊主订单优惠金额',
  `order_status` varchar(50) DEFAULT NULL COMMENT '订单状态,下单未支付（待付款）WAIT_BUYER_PAY 已付款未发货 WAIT_SELLER_SEND_GOODS 已付款，卖家部分发货 SELLER_CONSIGNED_PART 已发货未付款（用于货到付款）WAIT_BUYER_CONFIRM_PAY (买家已签收,货到付款专用) TRADE_BUYER_SIGNED 已发货未确认（待收货）WAIT_BUYER_CONFIRM_GOODS 交易成功 （由买家确认收货或系统自动触发）TRADE_FINISHED 交易关闭（付款以前，卖家或买家主动关闭交易，包括平台自动关闭情况）TRADE_CLOSED PAID_FORBID_CONSIGN，该状态代表订单已付款但是处于禁止发货状态（例如线上下单线下门店发货）。注：部分退款，退款成功，订单状态为交易成功 部分退款，退款失败，订单状态为交易成功 全部退款，退款成功，订单状态为交易关闭 全部退款，退款失败，订单状态为交易成功 针对预付业务，付完尾款才是待发货状态，未付尾款且尾款未超时状态为待付款 预付业务中尾款支付超时则状态为交易关闭 ',
  `discount_price` decimal(10,2) DEFAULT NULL COMMENT '商品最终单价（平摊后的商品价格）',
  `item_discount_rate` decimal(10,2) DEFAULT NULL COMMENT '商品折扣率',
  `promotion_num` int(11) DEFAULT '0' COMMENT '子订单优惠笔数',
  `is_refund` tinyint(1) DEFAULT NULL COMMENT '是否退单',
  `refund_status` varchar(50) DEFAULT NULL COMMENT '退款状态',
  `uni_refund_status` varchar(50) DEFAULT NULL COMMENT '全渠道退款状态',
  `refund_fee` decimal(10,2) DEFAULT NULL COMMENT '退款金额',
  `uni_refund_id` varchar(100) DEFAULT NULL COMMENT '全渠道退款Id',
  `refund_id` varchar(100) DEFAULT NULL COMMENT '退款Id',
  `consign_time` datetime DEFAULT NULL COMMENT '发货时间',
  `logistics_company` varchar(300) DEFAULT NULL COMMENT '快递公司',
  `logistics_no` varchar(100) DEFAULT NULL COMMENT '快递单号',
  `created` datetime DEFAULT NULL COMMENT '当前主订单的下单时间',
  `pay_time` datetime DEFAULT NULL COMMENT '当前主订单的付款时间',
  `trade_type` varchar(50) DEFAULT NULL COMMENT '当前主订单的交易类型,全渠道的',
  `modified` datetime DEFAULT NULL COMMENT '修改时间',
  `insert_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '第一次插入数据库时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据库更新时间',
  `guider` varchar(100) COMMENT '导购主题名称',
  `buyer_is_rate` varchar(32) COMMENT '买家是否已评价',
  `order_item_status` varchar(50) COMMENT '子订单状态',
  `category_id` varchar(100) COMMENT '平台原始商品类目ID',
  `detail_url` varchar(300) COMMENT '成交时商品详情页面URL',
  `pic_url` varchar(300) COMMENT '成交时商品图片URL',
  `guider_id` varchar(50) COMMENT '主播/达人id',
	`box_fee` decimal(10, 2) DEFAULT '0.00' COMMENT '包装费',
  `remark` varchar(2000) COMMENT '备注',
	`refund_product_num` int COMMENT '退款商品数量',
	`point_use` int COMMENT '商品支付用于抵现的积分数',
  `point_fee` decimal(10, 2) COMMENT '商品支付使用积分抵扣的订单金额',
  PRIMARY KEY (`uni_order_item_id`)
) DISTRIBUTED BY HASH(`uni_order_item_id`) INDEX_ALL='Y'  block_size=4096  COMMENT='子订单信息';

-- 商品信息
CREATE TABLE IF NOT EXISTS `ccms_product_${tenantId}` (
  `uni_product_id` varchar(100) DEFAULT NULL COMMENT '全渠道商品ID',
  `tenant` varchar(50) NOT NULL COMMENT '租户id',
  `product_id` varchar(150) DEFAULT NULL COMMENT '商品ID',
  `data_from` tinyint(4) DEFAULT NULL COMMENT '数据来源 0：ETL,1：标准API,2：用户导入',
  `partner` varchar(50) NOT NULL COMMENT '合作伙伴',
  `plat_code` varchar(100) DEFAULT NULL COMMENT '平台代码',
  `shop_id` varchar(150) DEFAULT NULL COMMENT '店铺ID',
  `uni_shop_id` varchar(100) DEFAULT NULL COMMENT '全渠道店铺ID',
  `category_id` varchar(100) DEFAULT NULL COMMENT '类目ID',
  `product_name` varchar(300) DEFAULT NULL COMMENT '商品名称',
  `type` varchar(50) DEFAULT NULL COMMENT '商品类型',
  `stock` int(11) DEFAULT NULL COMMENT '总库存数',
  `price` decimal(13,2) DEFAULT NULL COMMENT '商品售价',
  `detail_url` varchar(300) DEFAULT NULL COMMENT '商品详情页面URL',
  `pic_url` varchar(300) DEFAULT NULL COMMENT '商品主图URL',
  `modified` datetime DEFAULT NULL COMMENT '商品修改时间',
  `online_time` datetime DEFAULT NULL COMMENT '上架时间',
  `offline_time` datetime DEFAULT NULL COMMENT '下架时间',
  `outer_product_id` varchar(150) DEFAULT NULL COMMENT '外部商品id',
  `status` varchar(50) DEFAULT NULL COMMENT '商品状态,上架（SY_ONLINE）下架（SY_OFFLINE）售罄（SY_SAIL_OVER）',
  `seller_cid` varchar(300) DEFAULT NULL COMMENT '卖家自定义类目ID,格式[c1,c2,c3]',
  `insert_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '插入数据库时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '插入数据库时间',
	`product_score` decimal(13, 2) COMMENT '商品评分',
  PRIMARY KEY (`uni_product_id`)
) DISTRIBUTED BY HASH(`uni_product_id`) INDEX_ALL='Y'  block_size=4096  COMMENT='商品表';

-- 类目信息
CREATE TABLE IF NOT EXISTS `ccms_category_${tenantId}` (
  `category_id` varchar(150) NOT NULL COMMENT '商品类目ID',
  `parent_category_id` varchar(150) NOT NULL COMMENT '商品类目父ID',
  `data_from` tinyint(4) DEFAULT NULL COMMENT '数据来源 0：ETL,1：标准API,2：用户导入',
  `partner` varchar(50) NOT NULL COMMENT '合作伙伴',
  `plat_code` varchar(100) DEFAULT NULL COMMENT '平台代码',
  `shop_id` varchar(150) DEFAULT NULL COMMENT '店铺ID',
  `tenant` varchar(50) NOT NULL COMMENT '租户id',
  `category_name` varchar(200) DEFAULT NULL COMMENT '商品类目名称',
  `insert_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '插入数据库时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '插入数据库时间',
  PRIMARY KEY (`category_id`,`plat_code`,`shop_id`,`partner`)
) DISTRIBUTED BY HASH(`category_id`) INDEX_ALL='Y'  block_size=4096  COMMENT='商品类目';

-- 全渠道客户映射关系
CREATE TABLE  IF NOT EXISTS `g_plataccount_uniid_mapping_${tenantId}` (
  `s_uni_id` varchar(100) COLLATE utf8_bin NOT NULL COMMENT '原店铺级uni_id',
  `uni_shop_id` varchar(100) COLLATE utf8_bin NOT NULL COMMENT '全渠道店铺id',
  `tenant` varchar(50) COLLATE utf8_bin NOT NULL COMMENT '租户id',
  `plat_code` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '平台代码',
  `partner` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '合作伙伴',
  `plat_account` varchar(255) COLLATE utf8_bin DEFAULT NULL COMMENT '平台账号, nick存储',
  `g_uni_id` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '全渠道uni_id',
  `insert_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`s_uni_id`,`uni_shop_id`)
) DISTRIBUTED BY HASH(`s_uni_id`) INDEX_ALL='Y'  block_size=4096;

--会员表
CREATE TABLE IF NOT EXISTS `member_${tenantId}` (
  `card_plan_id` varchar(100) NOT NULL COMMENT '卡计划',
  `member_id` varchar(100) NOT NULL COMMENT '会员ID',
  `card_number` varchar(100) DEFAULT NULL COMMENT '会员卡号,取各平台店铺关联的标准版会员卡对应卡号',
  `card_name` varchar(100) DEFAULT NULL COMMENT '会员卡名称,取各平台店铺关联的标准版会员卡对应卡号的会员卡名称',
  `bind_mobile` varchar(400) DEFAULT NULL COMMENT '绑定手机号',
  `name` varchar(300) DEFAULT NULL COMMENT '姓名',
  `birthday` int(11) DEFAULT NULL COMMENT '会员开卡时，等记的性别',
  `gender` varchar(1) DEFAULT NULL COMMENT '会员开卡时，登记的性别男：m女：f',
  `tenant` varchar(50) NOT NULL COMMENT '租户domain',
  `shop_id` varchar(50) DEFAULT NULL COMMENT '店铺id',
  `plat_code` varchar(100) DEFAULT NULL COMMENT '全渠道店铺ID',
  `uni_shop_id` varchar(100) DEFAULT NULL COMMENT '全渠道店铺ID',
  `guide_id` varchar(50) DEFAULT NULL COMMENT '导购id',
  `status` varchar(20) DEFAULT NULL COMMENT '会员状态，正常：NORMAL ，销户：CLOSED',
  `grade` int(11) DEFAULT NULL COMMENT '等级,取会员在该会员卡的等级，参见忠诚度当前等级',
  `grade_period` datetime DEFAULT NULL COMMENT '等级有效期,取该会员中卡忠诚度中设置的等级有效期',
  `available_point` int(11) DEFAULT NULL COMMENT '可用积分,取会员在该会员卡的可用积分，参见忠诚度当前可用积分',
  `total_point` int(11) DEFAULT NULL COMMENT '累计积分,该用户历史会员卡所累计获取的积分',
  `consumed_point` int(11) DEFAULT NULL COMMENT '已消耗积分,该用户历史会员卡所消耗获取的积分',
  `expired_point` int(11) DEFAULT NULL COMMENT '已过期积分,该用户历史会员卡所累计过期的积分',
  `base_last_modified` datetime DEFAULT NULL,
  `point_last_modified` datetime DEFAULT NULL,
  `grade_last_modified` datetime DEFAULT NULL,
  `created` datetime DEFAULT NULL COMMENT '开卡时间',
  `modified` datetime DEFAULT NULL COMMENT '修改时间',
  `insert_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '第一次插入数据库时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据库更新时间',
  PRIMARY KEY (`member_id`, `card_plan_id`)
) DISTRIBUTED BY HASH(`member_id`) INDEX_ALL='Y'  block_size=4096  COMMENT='会员表';

-- 会员平台账号绑定
CREATE TABLE IF NOT EXISTS `member_bind_rel_${tenantId}` (
  `card_plan_id` varchar(100) NOT NULL COMMENT '卡计划',
  `member_id` varchar(100) NOT NULL COMMENT '会员ID',
  `bind_mobile` varchar(400) COMMENT '绑定手机号',
  `mix_mobile` varchar(400) COMMENT '绑定手机号,混淆手机号（会员通专用）',
  `name` varchar(300) COMMENT '姓名',
  `birthday` int COMMENT '会员开卡时，等记的性别',
  `gender` varchar(1) COMMENT '会员开卡时，登记的性别男：m女：f',
  `uni_shop_id` varchar(100) COMMENT '全渠道店铺ID（绑卡店铺的全渠道店铺ID）  ',
  `plat_code` varchar(100) COMMENT '绑卡店铺的平台code  ',
  `plat_account` varchar(350) NOT NULL COMMENT '平台账号',
  `shop_id` varchar(50) COMMENT '店铺id',
  `partner` varchar(50) COMMENT '合作伙伴',
  `guide_id` varchar(50) COMMENT '导购id',
  `created` datetime COMMENT '开卡时间',
  `modified` datetime COMMENT '修改时间',
  `tenant` varchar(50) NOT NULL COMMENT '租户domain',
  `bind_status` int NOT NULL COMMENT '绑定状态  , 0是解绑，1是绑定',
  `insert_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '第一次插入数据库时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据库更新时间',
  `source_type` varchar(30) COMMENT 'IMPORT--导入、JOS_SERVICE--京东会员通、SERVICE--互动、TMALL_SERVICE--天猫会员通、YOUZAN_SERVICE--有赞会员通、DOUYIN_SERVICE--抖音会员通、WEIMOB_SERVICE--微信会员通、INACTIVATED_IMPORT--待激活导入、ACTIVATE--激活、OTHER--其他',
  `uni_id` varchar(100) default null COMMENT '全渠道客户id-店铺级',
  `activate_status` int DEFAULT -1 COMMENT '会员激活状态,0: 未激活；1:已激活(默认)',
  `activate_date` datetime default null COMMENT '会员激活时间',
  `activate_update_time` datetime default null COMMENT '激活状态更新时间',
  `bind_update_time` datetime default null COMMENT '会员绑定更新时间',
  primary key (`plat_account`,`uni_shop_id`)
) DISTRIBUTE BY HASH(`plat_account`) INDEX_ALL='Y' STORAGE_POLICY='HOT' BLOCK_SIZE=4096 COMMENT='会员绑定关系';

-- 积分历史
CREATE TABLE IF NOT EXISTS `point_his_${tenantId}` (
 `id` varchar(100) NOT NULL COMMENT '主键',
 `card_plan_id` varchar(100) NOT NULL COMMENT '卡计划',
 `member_id` varchar(100) NOT NULL COMMENT '会员ID',
 `change_value` int NOT NULL COMMENT '积分变化值,可正，可负',
 `change_time` datetime NOT NULL COMMENT '积分变化时间',
 `action_type` varchar(100) COMMENT '行为类型',
 `source` varchar(100) COMMENT '积分变更来源    ',
 `tenant` varchar(50) NOT NULL COMMENT '租户domain',
 `change_biz` varchar(50) DEFAULT NULL COMMENT '变更业务',
 `change_biz_source` varchar(50) DEFAULT NULL COMMENT '变更业务来源',
 `shop_id` varchar(20) DEFAULT NULL COMMENT '店铺ID',
 `remark` varchar(200) DEFAULT NULL COMMENT '备注',
 `plat_code` varchar(20) DEFAULT NULL COMMENT '平台Code',
 `uni_shop_id` varchar(100) DEFAULT NULL COMMENT '全渠道店铺ID',
 `sequence` varchar(300)  DEFAULT NULL COMMENT '流水号',
 `insert_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '第一次插入数据库时间',
 `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据库更新时间',
 primary key (`id`)
) DISTRIBUTE BY HASH(`id`) INDEX_ALL='Y' STORAGE_POLICY='HOT' COMMENT='会员积分变更表';

-- 等级历史
CREATE TABLE IF NOT EXISTS `grade_his_${tenantId}` (
 `id` varchar(100) NOT NULL COMMENT '主键',
 `card_plan_id` varchar(100) NOT NULL COMMENT '卡计划',
 `member_id` varchar(100) NOT NULL COMMENT '会员ID',
 `grade_before_change` int NOT NULL COMMENT '变更前等级',
 `grade_after_change` int NOT NULL COMMENT '等级后等级',
 `grade_period` datetime COMMENT '等级有效期,取该会员中卡忠诚度中设置的等级有效期',
 `change_time` datetime NOT NULL COMMENT '变更时间',
 `change_type` varchar(50) NOT NULL COMMENT '变更类型,UP:升级DOWN:降级 KEEP:保级',
 `change_source` varchar(100) NOT NULL COMMENT '变更来源',
 `tenant` varchar(50) NOT NULL COMMENT '租户domain',
 `insert_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '第一次插入数据库时间',
 `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据库更新时间',
 primary key (`id`)
) DISTRIBUTE BY HASH(`id`) INDEX_ALL='Y' STORAGE_POLICY='HOT' COMMENT='会员等级变更表';

-- 退款表
CREATE TABLE IF NOT EXISTS `ccms_refund_${tenantId}` (
  `uni_refund_id` varchar(100) NOT NULL COMMENT '全渠道退单ID',
  `tenant` varchar(50) NOT NULL COMMENT '租户id',
  `data_from` tinyint(4) DEFAULT NULL COMMENT '数据来源 0：ETL,1：标准API,2：用户导入',
  `partner` varchar(50) NOT NULL COMMENT '合作伙伴',
  `refund_id` varchar(100) NOT NULL COMMENT '退单ID',
  `created` datetime DEFAULT NULL COMMENT '退款申请时间',
  `uni_order_item_id` varchar(100) NOT NULL COMMENT '全渠道子订单ID',
  `uni_order_id` varchar(100) NOT NULL COMMENT '全渠道订单ID',
  `plat_code` varchar(100) NOT NULL COMMENT '平台代码',
  `order_item_id` varchar(100) NOT NULL COMMENT '子订单ID',
  `order_id` varchar(100) NOT NULL COMMENT '主订单ID',
  `uni_shop_id` varchar(100) NOT NULL COMMENT '全渠道店铺ID',
  `shop_id` varchar(50) DEFAULT NULL COMMENT '店铺ID',
  `sku_id` varchar(150) DEFAULT NULL COMMENT '商品SKU ID',
  `product_id` varchar(150) DEFAULT NULL COMMENT '商品ID',
  `uni_product_id` varchar(100) DEFAULT NULL COMMENT '全渠道商品ID',
  `refund_fee` decimal(10,2) DEFAULT NULL COMMENT '退单金额',
  `refund_reason` varchar(500) DEFAULT NULL COMMENT '退款原因',
  `store_card_refund` decimal(13,2) DEFAULT NULL COMMENT '退还的本金+权益金之和',
  `refund_basic_fee` decimal(13,2) DEFAULT NULL COMMENT '退还的本金',
  `refund_expand_fee` decimal(13,2) DEFAULT NULL COMMENT '退还的权益金',
  `good_return` varchar(50) DEFAULT NULL COMMENT '退款退货,退款退货（SY_RETURN_FEE_GOOD）仅退款(SY_ONLY_FEE)',
  `uni_refund_status` varchar(50) DEFAULT NULL COMMENT '全渠道的退单状态,SY_CHECKING 审核中（卖家内部审核过程中）SY_WAIT_BUYER_RETURN_GOODS (卖家已经同意退款，等待买家退货)SY_WAIT_SELLER_CONFIRM_GOODS (买家已经退货，等待卖家确认收货)SY_SELLER_REFUSE_BUYER (卖家拒绝退款)SY_REFUNDING 退款中（除了SY_WAIT_BUYER_RETURN_GOODS 、SY_WAIT_SELLER_CONFIRM_GOODS 、SY_SELLER_REFUSE_BUYER 之外的退款中状态 ，可使用此值映射）SY_REFUND_SUCC （退款成功）SY_REFUND_FAIL（退款失败）',
  `refund_status` varchar(50) DEFAULT NULL COMMENT '退单状态',
  `modified` datetime DEFAULT NULL COMMENT '修改时间',
  `refund_phase` INT(11) DEFAULT 0 COMMENT '标准值：0：未知 1：售前退款（确认收货前退款）2：售后退款（确认收货后退款）',
  `insert_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '第一次插入数据库时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据库更新时间',
	`product_num` int COMMENT '商品数量',
	`refund_point` int COMMENT '退单积分',
  PRIMARY KEY (`uni_refund_id`)
) DISTRIBUTED BY HASH(`uni_refund_id`) INDEX_ALL='Y'  block_size=4096  COMMENT='退款表';

-- 地区表
CREATE TABLE IF NOT EXISTS `sys_area` (
  `code` char(12) NOT NULL DEFAULT '' COMMENT '代码',
  `level` tinyint(1) NOT NULL DEFAULT 0 COMMENT '级别',
  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '名称',
  `is_end` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否有子项',
  `parent_code`char(12) DEFAULT NULL COMMENT '父级代码',
  `catlog` int(10) NOT NULL DEFAULT 0 COMMENT '城乡分类码',
  PRIMARY KEY (`code`)
) DISTRIBUTED BY HASH(`code`) INDEX_ALL='Y'  block_size=4096  COMMENT = '地区表';

-- 营销活动报告
