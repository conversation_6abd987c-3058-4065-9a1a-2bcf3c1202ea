2025-07-31 20:07:31 [main] INFO  com.jcoder.aitest.AitestApplication - Starting AitestApplication using Java 11.0.15.1 on my-thinkbook with PID 31948 (D:\myspace\my-code\bigmodel\aitest-project\target\classes started by 10683 in D:\myspace\my-code\bigmodel\aitest-project)
2025-07-31 20:07:31 [main] DEBUG com.jcoder.aitest.AitestApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-31 20:07:31 [main] INFO  com.jcoder.aitest.AitestApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-31 20:07:32 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Searching for mappers annotated with @Mapper
2025-07-31 20:07:32 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Using auto-configuration base package 'com.jcoder.aitest'
2025-07-31 20:07:32 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-31 20:07:32 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-31 20:07:32 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-31 20:07:32 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-31 20:07:32 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1058 ms
2025-07-31 20:07:33 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.insert
2025-07-31 20:07:33 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.delete
2025-07-31 20:07:33 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.deleteByMap
2025-07-31 20:07:33 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.deleteById
2025-07-31 20:07:33 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.deleteBatchIds
2025-07-31 20:07:33 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.update
2025-07-31 20:07:33 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.updateById
2025-07-31 20:07:33 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectById
2025-07-31 20:07:33 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectBatchIds
2025-07-31 20:07:33 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectByMap
2025-07-31 20:07:33 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectOne
2025-07-31 20:07:33 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectCount
2025-07-31 20:07:33 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectMaps
2025-07-31 20:07:33 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectMapsPage
2025-07-31 20:07:33 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectObjs
2025-07-31 20:07:33 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectList
2025-07-31 20:07:33 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectPage
2025-07-31 20:07:33 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.insert
2025-07-31 20:07:33 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.delete
2025-07-31 20:07:33 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.deleteByMap
2025-07-31 20:07:33 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.deleteById
2025-07-31 20:07:33 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.deleteBatchIds
2025-07-31 20:07:33 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.update
2025-07-31 20:07:33 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.updateById
2025-07-31 20:07:33 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectById
2025-07-31 20:07:33 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectBatchIds
2025-07-31 20:07:33 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectByMap
2025-07-31 20:07:33 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectOne
2025-07-31 20:07:33 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectCount
2025-07-31 20:07:33 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectMaps
2025-07-31 20:07:33 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectMapsPage
2025-07-31 20:07:33 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectObjs
2025-07-31 20:07:33 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectList
2025-07-31 20:07:33 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectPage
2025-07-31 20:07:34 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.insert
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.delete
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.deleteByMap
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.deleteById
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.deleteBatchIds
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.update
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.updateById
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectById
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectBatchIds
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectByMap
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectOne
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectCount
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectMaps
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectMapsPage
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectObjs
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectList
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectPage
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.insert
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.delete
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.deleteByMap
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.deleteById
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.deleteBatchIds
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.update
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.updateById
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectById
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectBatchIds
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectByMap
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectOne
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectCount
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectMaps
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectMapsPage
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectObjs
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectList
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectPage
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.insert
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.delete
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.deleteByMap
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.deleteById
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.deleteBatchIds
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.update
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.updateById
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectById
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectBatchIds
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectByMap
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectOne
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectCount
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectMaps
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectMapsPage
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectObjs
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectList
2025-07-31 20:07:34 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectPage
2025-07-31 20:07:34 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-07-31 20:07:34 [main] INFO  com.jcoder.aitest.AitestApplication - Started AitestApplication in 2.892 seconds (JVM running for 3.926)
2025-07-31 20:12:25 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 20:12:25 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-31 20:12:25 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-07-31 20:12:25 [http-nio-8080-exec-1] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is java.lang.IllegalArgumentException: When allowCredentials is true, allowedOrigins cannot contain the special value "*" since that cannot be set on the "Access-Control-Allow-Origin" response header. To allow credentials to a set of origins, list them explicitly or consider using "allowedOriginPatterns" instead.] with root cause
java.lang.IllegalArgumentException: When allowCredentials is true, allowedOrigins cannot contain the special value "*" since that cannot be set on the "Access-Control-Allow-Origin" response header. To allow credentials to a set of origins, list them explicitly or consider using "allowedOriginPatterns" instead.
	at org.springframework.web.cors.CorsConfiguration.validateAllowCredentials(CorsConfiguration.java:495)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:532)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1266)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1048)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
2025-07-31 20:12:31 [http-nio-8080-exec-2] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is java.lang.IllegalArgumentException: When allowCredentials is true, allowedOrigins cannot contain the special value "*" since that cannot be set on the "Access-Control-Allow-Origin" response header. To allow credentials to a set of origins, list them explicitly or consider using "allowedOriginPatterns" instead.] with root cause
java.lang.IllegalArgumentException: When allowCredentials is true, allowedOrigins cannot contain the special value "*" since that cannot be set on the "Access-Control-Allow-Origin" response header. To allow credentials to a set of origins, list them explicitly or consider using "allowedOriginPatterns" instead.
	at org.springframework.web.cors.CorsConfiguration.validateAllowCredentials(CorsConfiguration.java:495)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:532)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1266)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1048)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
2025-07-31 20:12:34 [http-nio-8080-exec-3] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is java.lang.IllegalArgumentException: When allowCredentials is true, allowedOrigins cannot contain the special value "*" since that cannot be set on the "Access-Control-Allow-Origin" response header. To allow credentials to a set of origins, list them explicitly or consider using "allowedOriginPatterns" instead.] with root cause
java.lang.IllegalArgumentException: When allowCredentials is true, allowedOrigins cannot contain the special value "*" since that cannot be set on the "Access-Control-Allow-Origin" response header. To allow credentials to a set of origins, list them explicitly or consider using "allowedOriginPatterns" instead.
	at org.springframework.web.cors.CorsConfiguration.validateAllowCredentials(CorsConfiguration.java:495)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:532)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1266)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1048)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
2025-07-31 20:13:22 [http-nio-8080-exec-4] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is java.lang.IllegalArgumentException: When allowCredentials is true, allowedOrigins cannot contain the special value "*" since that cannot be set on the "Access-Control-Allow-Origin" response header. To allow credentials to a set of origins, list them explicitly or consider using "allowedOriginPatterns" instead.] with root cause
java.lang.IllegalArgumentException: When allowCredentials is true, allowedOrigins cannot contain the special value "*" since that cannot be set on the "Access-Control-Allow-Origin" response header. To allow credentials to a set of origins, list them explicitly or consider using "allowedOriginPatterns" instead.
	at org.springframework.web.cors.CorsConfiguration.validateAllowCredentials(CorsConfiguration.java:495)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:532)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1266)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1048)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
2025-07-31 20:13:58 [main] INFO  com.jcoder.aitest.AitestApplication - Starting AitestApplication using Java 1.8.0_321 on my-thinkbook with PID 16828 (D:\myspace\my-code\bigmodel\aitest-project\target\classes started by 10683 in D:\myspace\my-code\bigmodel\aitest-project)
2025-07-31 20:13:58 [main] DEBUG com.jcoder.aitest.AitestApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-31 20:13:58 [main] INFO  com.jcoder.aitest.AitestApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-31 20:13:59 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Searching for mappers annotated with @Mapper
2025-07-31 20:13:59 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Using auto-configuration base package 'com.jcoder.aitest'
2025-07-31 20:14:00 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-31 20:14:00 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-31 20:14:00 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-31 20:14:00 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-31 20:14:00 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1708 ms
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.insert
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.delete
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.deleteByMap
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.deleteById
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.deleteBatchIds
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.update
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.updateById
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectById
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectBatchIds
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectByMap
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectOne
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectCount
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectMaps
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectMapsPage
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectObjs
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectList
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectPage
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.insert
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.delete
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.deleteByMap
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.deleteById
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.deleteBatchIds
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.update
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.updateById
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectById
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectBatchIds
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectByMap
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectOne
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectCount
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectMaps
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectMapsPage
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectObjs
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectList
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectPage
2025-07-31 20:14:01 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.insert
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.delete
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.deleteByMap
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.deleteById
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.deleteBatchIds
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.update
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.updateById
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectById
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectBatchIds
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectByMap
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectOne
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectCount
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectMaps
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectMapsPage
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectObjs
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectList
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectPage
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.insert
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.delete
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.deleteByMap
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.deleteById
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.deleteBatchIds
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.update
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.updateById
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectById
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectBatchIds
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectByMap
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectOne
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectCount
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectMaps
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectMapsPage
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectObjs
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectList
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectPage
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.insert
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.delete
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.deleteByMap
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.deleteById
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.deleteBatchIds
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.update
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.updateById
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectById
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectBatchIds
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectByMap
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectOne
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectCount
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectMaps
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectMapsPage
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectObjs
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectList
2025-07-31 20:14:01 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectPage
2025-07-31 20:14:01 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8080 is already in use
2025-07-31 20:14:01 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-31 20:14:01 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-31 20:14:01 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

2025-07-31 20:14:41 [main] INFO  com.jcoder.aitest.AitestApplication - Starting AitestApplication using Java 1.8.0_321 on my-thinkbook with PID 708 (D:\myspace\my-code\bigmodel\aitest-project\target\classes started by 10683 in D:\myspace\my-code\bigmodel\aitest-project)
2025-07-31 20:14:41 [main] DEBUG com.jcoder.aitest.AitestApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-31 20:14:41 [main] INFO  com.jcoder.aitest.AitestApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-31 20:14:41 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Searching for mappers annotated with @Mapper
2025-07-31 20:14:41 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Using auto-configuration base package 'com.jcoder.aitest'
2025-07-31 20:14:42 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-31 20:14:42 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-31 20:14:42 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-31 20:14:42 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-31 20:14:42 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1225 ms
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.insert
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.delete
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.deleteByMap
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.deleteById
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.deleteBatchIds
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.update
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.updateById
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectById
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectBatchIds
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectByMap
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectOne
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectCount
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectMaps
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectMapsPage
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectObjs
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectList
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectPage
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.insert
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.delete
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.deleteByMap
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.deleteById
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.deleteBatchIds
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.update
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.updateById
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectById
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectBatchIds
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectByMap
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectOne
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectCount
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectMaps
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectMapsPage
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectObjs
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectList
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectPage
2025-07-31 20:14:43 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.insert
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.delete
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.deleteByMap
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.deleteById
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.deleteBatchIds
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.update
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.updateById
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectById
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectBatchIds
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectByMap
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectOne
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectCount
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectMaps
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectMapsPage
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectObjs
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectList
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectPage
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.insert
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.delete
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.deleteByMap
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.deleteById
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.deleteBatchIds
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.update
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.updateById
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectById
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectBatchIds
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectByMap
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectOne
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectCount
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectMaps
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectMapsPage
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectObjs
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectList
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectPage
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.insert
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.delete
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.deleteByMap
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.deleteById
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.deleteBatchIds
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.update
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.updateById
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectById
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectBatchIds
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectByMap
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectOne
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectCount
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectMaps
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectMapsPage
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectObjs
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectList
2025-07-31 20:14:43 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectPage
2025-07-31 20:14:43 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-07-31 20:14:43 [main] INFO  com.jcoder.aitest.AitestApplication - Started AitestApplication in 2.877 seconds (JVM running for 3.29)
2025-07-31 20:15:07 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 20:15:07 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-31 20:15:07 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-31 20:15:07 [http-nio-8080-exec-1] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is java.lang.IllegalArgumentException: When allowCredentials is true, allowedOrigins cannot contain the special value "*" since that cannot be set on the "Access-Control-Allow-Origin" response header. To allow credentials to a set of origins, list them explicitly or consider using "allowedOriginPatterns" instead.] with root cause
java.lang.IllegalArgumentException: When allowCredentials is true, allowedOrigins cannot contain the special value "*" since that cannot be set on the "Access-Control-Allow-Origin" response header. To allow credentials to a set of origins, list them explicitly or consider using "allowedOriginPatterns" instead.
	at org.springframework.web.cors.CorsConfiguration.validateAllowCredentials(CorsConfiguration.java:495)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:532)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1266)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1048)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-07-31 20:16:28 [main] INFO  com.jcoder.aitest.AitestApplication - Starting AitestApplication using Java 1.8.0_321 on my-thinkbook with PID 40752 (D:\myspace\my-code\bigmodel\aitest-project\target\classes started by 10683 in D:\myspace\my-code\bigmodel\aitest-project)
2025-07-31 20:16:28 [main] DEBUG com.jcoder.aitest.AitestApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-31 20:16:28 [main] INFO  com.jcoder.aitest.AitestApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-31 20:16:28 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Searching for mappers annotated with @Mapper
2025-07-31 20:16:28 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Using auto-configuration base package 'com.jcoder.aitest'
2025-07-31 20:16:29 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-31 20:16:29 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-31 20:16:29 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-31 20:16:29 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-31 20:16:29 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1195 ms
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.insert
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.delete
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.deleteByMap
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.deleteById
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.deleteBatchIds
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.update
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.updateById
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectById
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectBatchIds
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectByMap
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectOne
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectCount
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectMaps
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectMapsPage
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectObjs
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectList
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceProjectMapper.selectPage
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.insert
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.delete
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.deleteByMap
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.deleteById
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.deleteBatchIds
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.update
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.updateById
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectById
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectBatchIds
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectByMap
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectOne
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectCount
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectMaps
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectMapsPage
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectObjs
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectList
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.FileInfoMapper.selectPage
2025-07-31 20:16:30 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.insert
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.delete
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.deleteByMap
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.deleteById
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.deleteBatchIds
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.update
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.updateById
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectById
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectBatchIds
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectByMap
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectOne
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectCount
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectMaps
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectMapsPage
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectObjs
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectList
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.ComplianceTaskMapper.selectPage
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.insert
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.delete
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.deleteByMap
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.deleteById
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.deleteBatchIds
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.update
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.updateById
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectById
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectBatchIds
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectByMap
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectOne
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectCount
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectMaps
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectMapsPage
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectObjs
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectList
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.KnowledgeBaseMapper.selectPage
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.insert
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.delete
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.deleteByMap
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.deleteById
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.deleteBatchIds
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.update
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.updateById
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectById
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectBatchIds
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectByMap
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectOne
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectCount
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectMaps
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectMapsPage
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectObjs
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectList
2025-07-31 20:16:30 [main] DEBUG c.b.m.core.MybatisConfiguration - addMappedStatement: com.jcoder.aitest.mapper.SysUserMapper.selectPage
2025-07-31 20:16:30 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-07-31 20:16:30 [main] INFO  com.jcoder.aitest.AitestApplication - Started AitestApplication in 2.706 seconds (JVM running for 3.12)
2025-07-31 20:16:51 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 20:16:51 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-31 20:16:51 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-31 20:16:51 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Starting...
2025-07-31 20:16:51 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Start completed.
2025-07-31 20:19:18 [http-nio-8080-exec-4] INFO  c.j.a.s.i.ComplianceProjectServiceImpl - �û� 1 ������Ŀ�ɹ�����ĿID: 1
2025-07-31 20:20:38 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Shutdown initiated...
2025-07-31 20:20:38 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Shutdown completed.
