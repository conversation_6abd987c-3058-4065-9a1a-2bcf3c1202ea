# Portal System Demo

这是一个基于 Spring Boot 和 Vue.js 的 Portal 系统演示，实现了以下功能：

## 功能特性

1. **Portal 系统**
   - 支持 Microsoft Entra ID 认证登录
   - 左侧菜单列举所有子系统
   - 通过 iframe 集成子系统

2. **FoodAgent 子系统**
   - 独立的子系统，无独立登录
   - 通过 JWT ticket 从 Portal 获取用户信息
   - 基本的食物管理功能

3. **认证机制**
   - Portal 使用 OAuth2 + Microsoft Entra ID
   - 子系统通过 JWT ticket 认证
   - 一次性 ticket 机制确保安全性

## 技术栈

- **后端**: Spring Boot 2.7.18, Spring Security, MyBatis Plus
- **前端**: Vue.js 3, Bootstrap 5, T<PERSON><PERSON>eaf
- **数据库**: MySQL 8.0
- **认证**: OAuth2, JWT

## 快速开始

### 前置条件

1. Java 8+
2. Maven 3.6+
3. ~~MySQL 8.0+~~ (已改为使用 H2 内存数据库，无需安装 MySQL)

### 运行步骤

1. **编译项目**
   ```bash
   mvn clean compile
   ```

2. **运行应用**
   ```bash
   mvn spring-boot:run
   ```

3. **访问系统**
   - 打开浏览器访问: http://localhost:8080
   - 点击 "Demo Mode" 进入演示模式（无需 Azure AD 配置）

### 🎉 系统已成功运行！

应用程序现在正在 http://localhost:8080 上运行，使用 H2 内存数据库，无需额外配置。

### 演示流程

1. **首页**: 访问 http://localhost:8080
2. **演示登录**: 点击 "Demo Mode (No Azure AD required)"
3. **Portal 主页**: 查看左侧子系统列表
4. **访问 FoodAgent**: 点击 "Food Agent" 子系统
5. **子系统认证**: 系统自动生成 JWT ticket 并跳转
6. **使用子系统**: 在 FoodAgent 中管理食物信息

## 配置说明

### 数据库配置

当前使用 H2 内存数据库，无需额外配置。如需切换到 MySQL，请：

1. 在 `pom.xml` 中取消注释 MySQL 依赖，注释 H2 依赖
2. 在 `src/main/resources/application.yml` 中修改数据库连接：

```yaml
spring:
  datasource:
    url: ******************************************************************************************************************************************
    username: root
    password: password
    driver-class-name: com.mysql.cj.jdbc.Driver
```

### Azure AD 配置（生产环境）

如需使用真实的 Microsoft Entra ID，请配置以下环境变量：

```bash
export AZURE_CLIENT_ID=your-client-id
export AZURE_CLIENT_SECRET=your-client-secret
export AZURE_TENANT_ID=your-tenant-id
```

## 项目结构

```
src/main/java/com/jcoder/aitest/
├── config/          # 配置类
├── controller/      # 控制器
├── entity/          # 实体类
├── mapper/          # MyBatis Mapper
├── service/         # 服务层
└── util/            # 工具类

src/main/resources/
├── templates/       # Thymeleaf 模板
├── application.yml  # 应用配置
└── schema.sql       # 数据库初始化脚本
```

## API 接口

### Portal API
- `POST /api/ticket/generate` - 生成子系统访问票据
- `GET /api/user/info` - 获取当前用户信息

### FoodAgent API
- `POST /foodagent/api/auth/ticket` - 票据认证
- `GET /foodagent/api/foods` - 获取食物列表
- `POST /foodagent/api/foods` - 添加食物

## 注意事项

1. 首次运行会自动创建数据库表和初始数据
2. 演示模式使用内存会话，重启后需重新登录
3. JWT 票据有效期为 24 小时
4. 生产环境请配置真实的 Azure AD 应用

## 故障排除

1. **数据库连接失败**: 检查 MySQL 服务是否启动，用户名密码是否正确
2. **编译失败**: 确保 Java 8+ 和 Maven 3.6+ 已正确安装
3. **页面无法访问**: 检查端口 8080 是否被占用

## 开发说明

这是一个演示项目，展示了 Portal 系统的基本架构和认证流程。在实际生产环境中，还需要考虑：

- 更完善的错误处理
- 日志记录和监控
- 性能优化
- 安全加固
- 集群部署
