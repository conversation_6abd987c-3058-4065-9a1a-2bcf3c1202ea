<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能运营系统</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .app-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.1rem;
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        
        .chat-container {
            flex: 1;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            max-height: 500px;
            background: #f8f9fa;
        }
        
        .message {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
            gap: 10px;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            background: #f0f0f0;
            flex-shrink: 0;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
            position: relative;
        }

        .message-time {
            font-size: 11px;
            color: #999;
            margin-top: 5px;
            text-align: right;
        }
        
        .message.user .message-content {
            background: #007bff;
            color: white;
            border-bottom-right-radius: 4px;
        }
        
        .message.assistant .message-content {
            background: white;
            border: 1px solid #e9ecef;
            border-bottom-left-radius: 4px;
        }
        
        .message.system .message-content {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            color: #6c757d;
            font-style: italic;
        }
        
        .chat-input {
            padding: 20px;
            border-top: 1px solid #e9ecef;
            background: white;
        }
        
        .input-row {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }
        
        .input-area {
            flex: 1;
        }
        
        .file-upload {
            margin-bottom: 10px;
        }
        
        .typing-indicator {
            display: flex;
            align-items: center;
            color: #6c757d;
            font-style: italic;
            margin-bottom: 10px;
        }
        
        .typing-dots {
            display: inline-flex;
            margin-left: 8px;
        }
        
        .typing-dots span {
            width: 4px;
            height: 4px;
            border-radius: 50%;
            background: #6c757d;
            margin: 0 1px;
            animation: typing 1.4s infinite ease-in-out;
        }
        
        .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
        .typing-dots span:nth-child(2) { animation-delay: -0.16s; }
        
        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }
        
        .chart-container {
            margin: 10px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        
        .table-container {
            margin: 10px 0;
            overflow-x: auto;
        }
        
        .error-message {
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
        }
        
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        
        code {
            background: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }

        .result-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
            font-size: 12px;
        }

        .result-table th,
        .result-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        .result-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }

        .result-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        .data-table h4 {
            margin: 10px 0 5px 0;
            color: #333;
        }

        .more-data {
            text-align: center;
            color: #666;
            font-style: italic;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="app-container">
            <div class="header">
                <h1>🤖 智能运营系统</h1>
                <p>基于AI的电商数据分析与运营助手</p>
            </div>
            
            <div class="chat-container">
                <div class="chat-messages" ref="messagesContainer">
                    <div v-for="message in messages" :key="message.id" :class="['message', message.role]">
                        <div class="message-avatar" v-if="message.role === 'assistant'">🤖</div>
                        <div class="message-content">
                            <div v-if="message.type === 'error'" class="error-message">
                                ❌ {{ message.content }}
                            </div>
                            <div v-else-if="message.type === 'chart'" class="chart-container">
                                <div v-html="formatMessage(message.content)"></div>
                                <div v-if="message.data && message.data.length > 0" class="data-table">
                                    <h4>📊 数据详情</h4>
                                    <table class="result-table">
                                        <thead>
                                            <tr>
                                                <th v-for="(value, key) in message.data[0]" :key="key">{{ key }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="(row, index) in message.data.slice(0, 10)" :key="index">
                                                <td v-for="(value, key) in row" :key="key">{{ value }}</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                    <div v-if="message.data.length > 10" class="more-data">
                                        ... 还有 {{ message.data.length - 10 }} 条数据
                                    </div>
                                </div>
                            </div>
                            <div v-else v-html="formatMessage(message.content)"></div>
                            <div v-if="message.timestamp" class="message-time">
                                {{ formatTime(message.timestamp) }}
                            </div>
                        </div>
                        <div class="message-avatar" v-if="message.role === 'user'">👤</div>
                    </div>
                    
                    <div v-if="isTyping" class="typing-indicator">
                        AI正在思考中
                        <div class="typing-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </div>
                </div>
                
                <div class="chat-input">
                    <div class="file-upload">
                        <el-upload
                            ref="upload"
                            :action="uploadUrl"
                            :data="uploadData"
                            :on-success="handleUploadSuccess"
                            :on-error="handleUploadError"
                            :before-upload="beforeUpload"
                            :show-file-list="true"
                            :auto-upload="true"
                            accept=".txt,.csv,.xlsx,.xls"
                            multiple>
                            <el-button size="small" type="primary">上传文件</el-button>
                            <template #tip>
                                <div class="el-upload__tip">
                                    支持 txt/csv/excel 文件，大小不超过1MB
                                </div>
                            </template>
                        </el-upload>
                    </div>
                    
                    <div class="input-row">
                        <div class="input-area">
                            <el-input
                                v-model="inputMessage"
                                type="textarea"
                                :rows="3"
                                placeholder="请输入您的问题，例如：查询今天的订单数量、如何提升客单价、对618活动进行分析..."
                                @keydown.ctrl.enter="sendMessage"
                                :disabled="isTyping">
                            </el-input>
                        </div>
                        <el-button 
                            type="primary" 
                            @click="sendMessage"
                            :loading="isTyping"
                            :disabled="!inputMessage.trim()">
                            发送
                        </el-button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        createApp({
            data() {
                return {
                    messages: [
                        {
                            id: 1,
                            role: 'system',
                            type: 'text',
                            content: '欢迎使用智能运营系统！我可以帮您进行数据查询、业务分析、运营诊断等。请告诉我您需要什么帮助？'
                        }
                    ],
                    inputMessage: '',
                    isTyping: false,
                    sessionId: null,
                    uploadedFiles: [],
                    uploadUrl: '/api/file/upload',
                    uploadData: {}
                }
            },
            
            mounted() {
                this.sessionId = this.generateSessionId();
                this.uploadData.sessionId = this.sessionId;
            },
            
            methods: {
                generateSessionId() {
                    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                },
                
                async sendMessage() {
                    if (!this.inputMessage.trim() || this.isTyping) return;
                    
                    const userMessage = {
                        id: Date.now(),
                        role: 'user',
                        type: 'text',
                        content: this.inputMessage,
                        timestamp: new Date()
                    };
                    
                    this.messages.push(userMessage);
                    
                    const requestData = {
                        sessionId: this.sessionId,
                        message: this.inputMessage,
                        tenantId: 'tenant001',
                        userId: 'user001',
                        fileIds: this.uploadedFiles.map(f => f.fileId)
                    };
                    
                    this.inputMessage = '';
                    this.isTyping = true;
                    this.scrollToBottom();
                    
                    try {
                        const response = await fetch('/api/chat/stream', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify(requestData)
                        });
                        
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        
                        const reader = response.body.getReader();
                        const decoder = new TextDecoder();
                        
                        let currentMessage = {
                            id: Date.now() + 1,
                            role: 'assistant',
                            type: 'text',
                            content: '',
                            data: null,
                            timestamp: new Date()
                        };
                        
                        this.messages.push(currentMessage);
                        
                        while (true) {
                            const { done, value } = await reader.read();
                            if (done) break;
                            
                            const chunk = decoder.decode(value);
                            const lines = chunk.split('\n');
                            
                            for (const line of lines) {
                                if (line.startsWith('data: ')) {
                                    try {
                                        const data = JSON.parse(line.substring(6));
                                        
                                        if (data.type === 'error') {
                                            currentMessage.type = 'error';
                                            currentMessage.content = data.content;
                                        } else if (data.type === 'chart') {
                                            currentMessage.type = 'chart';
                                            currentMessage.content = data.content;
                                            currentMessage.data = data.data;
                                        } else {
                                            currentMessage.content += data.content || '';
                                        }
                                        
                                        this.scrollToBottom();
                                        
                                        if (data.finished) {
                                            this.isTyping = false;
                                        }
                                    } catch (e) {
                                        console.warn('Failed to parse SSE data:', line);
                                    }
                                }
                            }
                        }
                        
                    } catch (error) {
                        console.error('Error:', error);
                        this.messages.push({
                            id: Date.now() + 2,
                            role: 'assistant',
                            type: 'error',
                            content: '抱歉，发生了错误：' + error.message
                        });
                    } finally {
                        this.isTyping = false;
                        this.scrollToBottom();
                    }
                },
                
                formatMessage(content) {
                    if (!content) return '';

                    // Convert markdown-like formatting
                    return content
                        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                        .replace(/\*(.*?)\*/g, '<em>$1</em>')
                        .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
                        .replace(/`(.*?)`/g, '<code>$1</code>')
                        .replace(/\n/g, '<br>');
                },

                formatTime(timestamp) {
                    if (!timestamp) return '';
                    const date = new Date(timestamp);
                    return date.toLocaleTimeString('zh-CN', {
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                },
                
                scrollToBottom() {
                    this.$nextTick(() => {
                        const container = this.$refs.messagesContainer;
                        container.scrollTop = container.scrollHeight;
                    });
                },
                
                beforeUpload(file) {
                    const isValidType = ['text/plain', 'text/csv', 'application/vnd.ms-excel', 
                                       'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'].includes(file.type);
                    const isValidSize = file.size / 1024 / 1024 < 1;
                    
                    if (!isValidType) {
                        ElMessage.error('只支持 txt, csv, excel 文件格式！');
                        return false;
                    }
                    if (!isValidSize) {
                        ElMessage.error('文件大小不能超过 1MB！');
                        return false;
                    }
                    return true;
                },
                
                handleUploadSuccess(response, file) {
                    if (response.success) {
                        this.uploadedFiles.push({
                            fileId: response.fileId,
                            fileName: response.fileName
                        });
                        ElMessage.success('文件上传成功！');
                    } else {
                        ElMessage.error(response.message || '文件上传失败！');
                    }
                },
                
                handleUploadError(error) {
                    console.error('Upload error:', error);
                    ElMessage.error('文件上传失败！');
                }
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
