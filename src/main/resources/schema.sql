-- H2 Database Schema

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    azure_id VARCHAR(255) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL,
    name <PERSON><PERSON><PERSON><PERSON>(255),
    display_name VA<PERSON>HA<PERSON>(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0
);

CREATE INDEX IF NOT EXISTS idx_azure_id ON users(azure_id);
CREATE INDEX IF NOT EXISTS idx_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_deleted ON users(deleted);

-- Subsystems table
CREATE TABLE IF NOT EXISTS subsystems (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    display_name VA<PERSON>HA<PERSON>(255) NOT NULL,
    url VARCHAR(500) NOT NULL,
    description TEXT,
    status TINYINT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0
);

CREATE INDEX IF NOT EXISTS idx_name ON subsystems(name);
CREATE INDEX IF NOT EXISTS idx_status ON subsystems(status);
CREATE INDEX IF NOT EXISTS idx_deleted_sub ON subsystems(deleted);

-- Tickets table
CREATE TABLE IF NOT EXISTS tickets (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    ticket_id VARCHAR(255) NOT NULL UNIQUE,
    user_id BIGINT NOT NULL,
    subsystem VARCHAR(100) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    used TINYINT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    used_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE INDEX IF NOT EXISTS idx_ticket_id ON tickets(ticket_id);
CREATE INDEX IF NOT EXISTS idx_user_id ON tickets(user_id);
CREATE INDEX IF NOT EXISTS idx_subsystem ON tickets(subsystem);
CREATE INDEX IF NOT EXISTS idx_expires_at ON tickets(expires_at);
CREATE INDEX IF NOT EXISTS idx_used ON tickets(used);

-- Insert default subsystem (H2 compatible)
INSERT INTO subsystems (name, display_name, url, description, status, created_at, updated_at, deleted)
VALUES ('foodagent', 'Food Agent', '/foodagent', 'Food management system', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0);
