<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portal - Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #f8f9fa;
            border-right: 1px solid #dee2e6;
            position: fixed;
            width: 250px;
            top: 56px;
            left: 0;
            overflow-y: auto;
        }
        .content-area {
            margin-left: 250px;
            margin-top: 56px;
            height: calc(100vh - 56px);
        }
        .content-frame {
            width: 100%;
            height: 100%;
            border: none;
        }
        .subsystem-item {
            cursor: pointer;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
            transition: all 0.3s;
            display: flex;
            align-items: center;
        }
        .subsystem-item:hover {
            background-color: #e9ecef;
        }
        .subsystem-item.active {
            background-color: #007bff;
            color: white;
        }
        .subsystem-item i {
            margin-right: 10px;
            width: 20px;
        }
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }
        .welcome-content {
            padding: 40px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- Navigation Bar -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-portal-enter me-2"></i>
                    Portal System
                </a>
                <div class="navbar-nav ms-auto">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            <span th:text="${user?.displayName ?: 'User'}">User</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Sidebar -->
        <div class="sidebar">
            <div class="p-3">
                <h6 class="text-muted mb-3">APPLICATIONS</h6>
            </div>
            <div v-for="subsystem in subsystems" :key="subsystem.id" 
                 class="subsystem-item" 
                 :class="{ active: activeSubsystem?.id === subsystem.id }"
                 @click="loadSubsystem(subsystem)">
                <i class="fas fa-cube"></i>
                <div>
                    <div class="fw-bold">{{ subsystem.displayName }}</div>
                    <small class="text-muted">{{ subsystem.description }}</small>
                </div>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            <div v-if="!activeSubsystem" class="welcome-content">
                <h2>Welcome to Portal System</h2>
                <p class="text-muted">Select an application from the sidebar to get started</p>
                <div class="row mt-4">
                    <div class="col-md-4" v-for="subsystem in subsystems" :key="subsystem.id">
                        <div class="card mb-3" style="cursor: pointer;" @click="loadSubsystem(subsystem)">
                            <div class="card-body text-center">
                                <i class="fas fa-cube fa-3x text-primary mb-3"></i>
                                <h5 class="card-title">{{ subsystem.displayName }}</h5>
                                <p class="card-text">{{ subsystem.description }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <iframe v-if="activeSubsystem" 
                    :src="iframeUrl" 
                    class="content-frame"
                    @load="onIframeLoad">
            </iframe>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script th:inline="javascript">
        const { createApp } = Vue;
        
        createApp({
            data() {
                return {
                    subsystems: /*[[${subsystems}]]*/ [],
                    activeSubsystem: null,
                    iframeUrl: '',
                    user: /*[[${user}]]*/ null
                };
            },
            methods: {
                async loadSubsystem(subsystem) {
                    try {
                        // Generate ticket for the subsystem
                        const response = await axios.post('/api/ticket/generate', 
                            new URLSearchParams({ subsystem: subsystem.name }));
                        
                        if (response.data.success) {
                            this.activeSubsystem = subsystem;
                            this.iframeUrl = `${subsystem.url}?ticket=${response.data.ticket}`;
                        } else {
                            alert('Failed to generate access ticket: ' + response.data.message);
                        }
                    } catch (error) {
                        console.error('Error loading subsystem:', error);
                        alert('Failed to load subsystem');
                    }
                },
                onIframeLoad() {
                    console.log('Subsystem loaded successfully');
                }
            }
        }).mount('#app');
    </script>
</body>
</html>
