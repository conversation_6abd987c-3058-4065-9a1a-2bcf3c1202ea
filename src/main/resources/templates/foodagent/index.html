<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Food Agent</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .navbar {
            background: linear-gradient(45deg, #28a745, #20c997);
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .btn-success {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
        }
        .loading {
            text-align: center;
            padding: 50px;
        }
        .authenticated {
            display: none;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark">
            <div class="container">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-utensils me-2"></i>
                    Food Agent
                </a>
                <div class="navbar-nav ms-auto" v-if="user">
                    <span class="navbar-text">
                        Welcome, {{ user.displayName }}
                    </span>
                </div>
            </div>
        </nav>

        <!-- Loading State -->
        <div v-if="loading" class="loading">
            <div class="spinner-border text-success" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3">Authenticating...</p>
        </div>

        <!-- Error State -->
        <div v-if="error" class="container mt-4">
            <div class="alert alert-danger" role="alert">
                <h4 class="alert-heading">Authentication Failed</h4>
                <p>{{ error }}</p>
            </div>
        </div>

        <!-- Main Content -->
        <div v-if="authenticated" class="container mt-4">
            <div class="row">
                <div class="col-md-12">
                    <h2>Welcome to Food Agent System</h2>
                    <p class="text-muted">Manage your food inventory and recipes</p>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-list fa-3x text-success mb-3"></i>
                            <h5 class="card-title">Food Inventory</h5>
                            <p class="card-text">Manage your food items and inventory</p>
                            <button class="btn btn-success" @click="showFoods = true">
                                View Foods
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-plus fa-3x text-success mb-3"></i>
                            <h5 class="card-title">Add Food</h5>
                            <p class="card-text">Add new food items to inventory</p>
                            <button class="btn btn-success" @click="showAddForm = true">
                                Add Food
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-chart-bar fa-3x text-success mb-3"></i>
                            <h5 class="card-title">Reports</h5>
                            <p class="card-text">View food statistics and reports</p>
                            <button class="btn btn-success">
                                View Reports
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Food List Modal -->
            <div class="modal fade" :class="{ show: showFoods }" :style="{ display: showFoods ? 'block' : 'none' }" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Food Inventory</h5>
                            <button type="button" class="btn-close" @click="showFoods = false"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-4 mb-2" v-for="food in foods" :key="food">
                                    <div class="card">
                                        <div class="card-body text-center">
                                            <i class="fas fa-apple-alt text-success"></i>
                                            {{ food }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Add Food Modal -->
            <div class="modal fade" :class="{ show: showAddForm }" :style="{ display: showAddForm ? 'block' : 'none' }" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Add New Food</h5>
                            <button type="button" class="btn-close" @click="showAddForm = false"></button>
                        </div>
                        <div class="modal-body">
                            <form @submit.prevent="addFood">
                                <div class="mb-3">
                                    <label for="foodName" class="form-label">Food Name</label>
                                    <input type="text" class="form-control" id="foodName" v-model="newFoodName" required>
                                </div>
                                <button type="submit" class="btn btn-success">Add Food</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script th:inline="javascript">
        const { createApp } = Vue;
        
        createApp({
            data() {
                return {
                    loading: true,
                    authenticated: false,
                    error: null,
                    user: null,
                    foods: [],
                    showFoods: false,
                    showAddForm: false,
                    newFoodName: '',
                    ticket: /*[[${ticket}]]*/ null
                };
            },
            async mounted() {
                await this.authenticate();
            },
            methods: {
                async authenticate() {
                    if (!this.ticket) {
                        this.error = 'No authentication ticket provided';
                        this.loading = false;
                        return;
                    }

                    try {
                        const response = await axios.post('/foodagent/api/auth/ticket', 
                            new URLSearchParams({ ticket: this.ticket }));
                        
                        if (response.data.success) {
                            this.user = response.data.user;
                            this.authenticated = true;
                            await this.loadFoods();
                        } else {
                            this.error = response.data.message;
                        }
                    } catch (error) {
                        this.error = 'Authentication failed: ' + error.message;
                    } finally {
                        this.loading = false;
                    }
                },
                async loadFoods() {
                    try {
                        const response = await axios.get('/foodagent/api/foods');
                        if (response.data.success) {
                            this.foods = response.data.foods;
                        }
                    } catch (error) {
                        console.error('Failed to load foods:', error);
                    }
                },
                async addFood() {
                    if (!this.newFoodName.trim()) return;

                    try {
                        const response = await axios.post('/foodagent/api/foods', 
                            new URLSearchParams({ name: this.newFoodName }));
                        
                        if (response.data.success) {
                            this.foods.push(this.newFoodName);
                            this.newFoodName = '';
                            this.showAddForm = false;
                            alert('Food added successfully!');
                        } else {
                            alert('Failed to add food: ' + response.data.message);
                        }
                    } catch (error) {
                        alert('Failed to add food: ' + error.message);
                    }
                }
            }
        }).mount('#app');
    </script>
</body>
</html>
