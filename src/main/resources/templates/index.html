<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portal System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
        }
    </style>
</head>
<body>
    <div class="hero-section">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8 text-center">
                    <h1 class="display-4 mb-4">Welcome to Portal System</h1>
                    <p class="lead mb-5">A unified platform for accessing all your business applications</p>
                    <div class="card mx-auto" style="max-width: 400px;">
                        <div class="card-body p-5">
                            <h3 class="card-title mb-4">Get Started</h3>
                            <p class="card-text mb-4">Sign in with your Microsoft account to access the portal</p>
                            <a href="/login" class="btn btn-primary btn-lg mb-3">
                                <i class="fab fa-microsoft me-2"></i>
                                Sign in with Microsoft
                            </a>
                            <br>
                            <a href="/demo/login" class="btn btn-outline-primary">
                                <i class="fas fa-play me-2"></i>
                                Demo Mode (No Azure AD required)
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
</body>
</html>
