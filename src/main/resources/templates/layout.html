<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${title ?: 'Portal System'}">Portal System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #f8f9fa;
            border-right: 1px solid #dee2e6;
        }
        .content-frame {
            width: 100%;
            height: calc(100vh - 56px);
            border: none;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .subsystem-item {
            cursor: pointer;
            padding: 10px 15px;
            border-bottom: 1px solid #dee2e6;
            transition: background-color 0.2s;
        }
        .subsystem-item:hover {
            background-color: #e9ecef;
        }
        .subsystem-item.active {
            background-color: #007bff;
            color: white;
        }
    </style>
</head>
<body>
    <div th:fragment="layout (content)">
        <div th:replace="${content}"></div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</body>
</html>
