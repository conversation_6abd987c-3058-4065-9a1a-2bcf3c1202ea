server:
  port: 8080

spring:
  application:
    name: aitest-project

  # Database Configuration (H2 for demo)
  datasource:
    url: jdbc:h2:mem:aitest;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password:
    driver-class-name: org.h2.Driver

  # H2 Console (for debugging)
  h2:
    console:
      enabled: true
      path: /h2-console

  # SQL initialization
  sql:
    init:
      mode: always
      schema-locations: classpath:schema.sql

  # OAuth2 Configuration for Microsoft Entra ID
  security:
    oauth2:
      client:
        registration:
          azure:
            client-id: ${AZURE_CLIENT_ID:demo-client-id}
            client-secret: ${AZURE_CLIENT_SECRET:demo-client-secret}
            scope: openid,profile,email
            authorization-grant-type: authorization_code
            redirect-uri: "{baseUrl}/login/oauth2/code/{registrationId}"
        provider:
          azure:
            authorization-uri: https://login.microsoftonline.com/${AZURE_TENANT_ID:demo-tenant-id}/oauth2/v2.0/authorize
            token-uri: https://login.microsoftonline.com/${AZURE_TENANT_ID:demo-tenant-id}/oauth2/v2.0/token
            user-info-uri: https://graph.microsoft.com/v1.0/me
            user-name-attribute: id

# MyBatis Plus Configuration
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# JWT Configuration
jwt:
  secret: ${JWT_SECRET:mySecretKey123456789012345678901234567890}
  expiration: 86400000 # 24 hours in milliseconds

# Portal Configuration
portal:
  base-url: http://localhost:8080
  subsystems:
    - name: foodagent
      display-name: Food Agent
      url: /foodagent
      description: Food management system

# Logging Configuration
logging:
  level:
    com.jcoder.aitest: debug
    org.springframework: info
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
