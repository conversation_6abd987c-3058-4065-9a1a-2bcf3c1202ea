server:
  port: 8080

spring:
  application:
    name: aitest-project

  # Database Configuration
  datasource:
    url: *************************************************************************************************************
    username: root
    password: password
    driver-class-name: com.mysql.cj.jdbc.Driver

  # OAuth2 Configuration for Microsoft Entra ID
  security:
    oauth2:
      client:
        registration:
          azure:
            client-id: ${AZURE_CLIENT_ID:your-client-id}
            client-secret: ${AZURE_CLIENT_SECRET:your-client-secret}
            scope: openid,profile,email
            authorization-grant-type: authorization_code
            redirect-uri: "{baseUrl}/login/oauth2/code/{registrationId}"
        provider:
          azure:
            authorization-uri: https://login.microsoftonline.com/${AZURE_TENANT_ID:your-tenant-id}/oauth2/v2.0/authorize
            token-uri: https://login.microsoftonline.com/${AZURE_TENANT_ID:your-tenant-id}/oauth2/v2.0/token
            user-info-uri: https://graph.microsoft.com/v1.0/me
            user-name-attribute: id

# MyBatis Plus Configuration
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# JWT Configuration
jwt:
  secret: ${JWT_SECRET:mySecretKey123456789012345678901234567890}
  expiration: 86400000 # 24 hours in milliseconds

# Portal Configuration
portal:
  base-url: http://localhost:8080
  subsystems:
    - name: foodagent
      display-name: Food Agent
      url: /foodagent
      description: Food management system

# Logging Configuration
logging:
  level:
    com.jcoder.aitest: debug
    org.springframework: info
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
