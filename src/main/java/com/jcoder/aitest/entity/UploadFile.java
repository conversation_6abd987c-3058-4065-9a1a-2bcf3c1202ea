package com.jcoder.aitest.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Upload File Entity
 * 
 * <AUTHOR>
 */
@Data
@TableName("upload_file")
public class UploadFile {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String fileId;
    private String sessionId;
    private String originalName;
    private String fileName;
    private String filePath;
    private String fileType;
    private Long fileSize;
    private String status;
    private String content; // Extracted content
    private LocalDateTime createTime;
}
