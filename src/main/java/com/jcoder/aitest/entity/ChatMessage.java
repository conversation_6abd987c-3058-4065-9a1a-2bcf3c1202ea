package com.jcoder.aitest.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Chat Message Entity
 * 
 * <AUTHOR>
 */
@Data
@TableName("chat_message")
public class ChatMessage {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String sessionId;
    private String messageId;
    private String role; // user, assistant, system
    private String content;
    private String messageType; // text, file, chart
    private String metadata; // JSON format
    private LocalDateTime createTime;
}
