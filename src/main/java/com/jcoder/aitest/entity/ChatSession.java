package com.jcoder.aitest.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Chat Session Entity
 * 
 * <AUTHOR>
 */
@Data
@TableName("chat_session")
public class ChatSession {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String sessionId;
    private String tenantId;
    private String userId;
    private String title;
    private String status;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
}
