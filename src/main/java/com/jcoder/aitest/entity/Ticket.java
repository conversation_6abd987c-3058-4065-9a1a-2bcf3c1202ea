package com.jcoder.aitest.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDateTime;

/**
 * Ticket Entity for JWT authentication
 */
@TableName("tickets")
public class Ticket {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String ticketId;
    private Long userId;
    private String subsystem;
    private LocalDateTime expiresAt;
    private Integer used; // 0: not used, 1: used
    private LocalDateTime createdAt;
    private LocalDateTime usedAt;
    
    public Ticket() {}
    
    public Ticket(String ticketId, Long userId, String subsystem, LocalDateTime expiresAt) {
        this.ticketId = ticketId;
        this.userId = userId;
        this.subsystem = subsystem;
        this.expiresAt = expiresAt;
        this.used = 0;
        this.createdAt = LocalDateTime.now();
    }
    
    // Get<PERSON> and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getTicketId() {
        return ticketId;
    }
    
    public void setTicketId(String ticketId) {
        this.ticketId = ticketId;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public String getSubsystem() {
        return subsystem;
    }
    
    public void setSubsystem(String subsystem) {
        this.subsystem = subsystem;
    }
    
    public LocalDateTime getExpiresAt() {
        return expiresAt;
    }
    
    public void setExpiresAt(LocalDateTime expiresAt) {
        this.expiresAt = expiresAt;
    }
    
    public Integer getUsed() {
        return used;
    }
    
    public void setUsed(Integer used) {
        this.used = used;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUsedAt() {
        return usedAt;
    }
    
    public void setUsedAt(LocalDateTime usedAt) {
        this.usedAt = usedAt;
    }
}
