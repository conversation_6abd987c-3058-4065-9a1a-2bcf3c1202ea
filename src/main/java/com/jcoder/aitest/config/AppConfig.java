package com.jcoder.aitest.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Application Configuration
 * 
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "app")
public class AppConfig {
    
    private TenantConfig tenant;
    private UploadConfig upload;
    private RagConfig rag;
    
    @Data
    public static class TenantConfig {
        private String defaultTenant;
    }
    
    @Data
    public static class UploadConfig {
        private String path;
    }
    
    @Data
    public static class RagConfig {
        private String knowledgeBasePath;
    }
}
