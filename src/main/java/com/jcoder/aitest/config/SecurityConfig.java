package com.jcoder.aitest.config;

import com.jcoder.aitest.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.oauth2.client.userinfo.DefaultOAuth2UserService;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserRequest;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserService;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.security.web.SecurityFilterChain;

/**
 * Security Configuration
 */
@Configuration
@EnableWebSecurity
public class SecurityConfig {
    
    @Autowired
    private UserService userService;
    
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            .authorizeHttpRequests(authz -> authz
                .requestMatchers("/", "/login", "/error", "/static/**", "/css/**", "/js/**", "/images/**").permitAll()
                .requestMatchers("/foodagent/api/auth/**").permitAll() // Allow foodagent auth endpoints
                .requestMatchers("/foodagent/**").permitAll() // Allow foodagent access
                .anyRequest().authenticated()
            )
            .oauth2Login(oauth2 -> oauth2
                .loginPage("/login")
                .defaultSuccessUrl("/portal", true)
                .userInfoEndpoint(userInfo -> userInfo
                    .userService(oauth2UserService())
                )
            )
            .logout(logout -> logout
                .logoutSuccessUrl("/")
                .invalidateHttpSession(true)
                .clearAuthentication(true)
            )
            .csrf(csrf -> csrf
                .ignoringRequestMatchers("/foodagent/api/**") // Disable CSRF for API endpoints
            );
        
        return http.build();
    }
    
    @Bean
    public OAuth2UserService<OAuth2UserRequest, OAuth2User> oauth2UserService() {
        DefaultOAuth2UserService delegate = new DefaultOAuth2UserService();
        
        return (userRequest) -> {
            OAuth2User oauth2User = delegate.loadUser(userRequest);
            
            // Extract user information from Azure AD
            String azureId = oauth2User.getAttribute("id");
            String email = oauth2User.getAttribute("mail");
            if (email == null) {
                email = oauth2User.getAttribute("userPrincipalName");
            }
            String name = oauth2User.getAttribute("givenName");
            String displayName = oauth2User.getAttribute("displayName");
            
            // Create or update user in database
            userService.createOrUpdateUser(azureId, email, name, displayName);
            
            return oauth2User;
        };
    }
}
