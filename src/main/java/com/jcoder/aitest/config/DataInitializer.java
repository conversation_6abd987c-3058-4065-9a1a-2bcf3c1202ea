package com.jcoder.aitest.config;

import com.jcoder.aitest.service.SubsystemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * Data Initializer
 */
@Component
public class DataInitializer implements CommandLineRunner {
    
    @Autowired
    private SubsystemService subsystemService;
    
    @Override
    public void run(String... args) throws Exception {
        // Initialize default subsystems
        subsystemService.initializeDefaultSubsystems();
    }
}
