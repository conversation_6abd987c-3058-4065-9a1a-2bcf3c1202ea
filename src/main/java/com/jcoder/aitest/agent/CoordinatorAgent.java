package com.jcoder.aitest.agent;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jcoder.aitest.dto.ChatRequest;
import com.jcoder.aitest.dto.ChatResponse;
import com.jcoder.aitest.entity.ChatMessage;
import com.jcoder.aitest.entity.ChatSession;
import com.jcoder.aitest.entity.UploadFile;
import com.jcoder.aitest.mapper.ChatMessageMapper;
import com.jcoder.aitest.util.SseWriter;
import com.jcoder.aitest.mapper.ChatSessionMapper;
import com.jcoder.aitest.mapper.UploadFileMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Coordinator Agent - Coordinates between different agents
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class CoordinatorAgent {
    
    @Autowired
    private List<BaseAgent> agents;
    
    @Autowired
    private ChatSessionMapper chatSessionMapper;
    
    @Autowired
    private ChatMessageMapper chatMessageMapper;
    
    @Autowired
    private UploadFileMapper uploadFileMapper;
    
    /**
     * Process chat request
     * 
     * @param request Chat request
     * @return Streaming response
     */
    public Flux<ChatResponse> processRequest(ChatRequest request) {
        return Flux.create(sink -> {
            try {
                // Create or get session
                String sessionId = getOrCreateSession(request);
                request.setSessionId(sessionId);
                
                // Save user message
                saveUserMessage(request);
                
                // Get file content if any
                String fileContent = getFileContent(request);
                if (fileContent != null && !fileContent.isEmpty()) {
                    request.setMessage(request.getMessage() + "\n\n附件内容：\n" + fileContent);
                }
                
                // Find the most suitable agent
                BaseAgent selectedAgent = selectAgent(request);
                log.info("Selected agent: {}", selectedAgent.getAgentName());
                
                // Process with selected agent
                selectedAgent.process(request)
                        .doOnNext(response -> {
                            // Save assistant message when finished
                            if (response.isFinished() && !"error".equals(response.getType())) {
                                saveAssistantMessage(response);
                            }
                            sink.next(response);
                        })
                        .doOnComplete(sink::complete)
                        .doOnError(error -> {
                            log.error("Agent processing error", error);
                            sink.error(error);
                        })
                        .subscribe();
                
            } catch (Exception e) {
                log.error("Coordinator processing error", e);
                sink.next(ChatResponse.error(request.getSessionId(), UUID.randomUUID().toString(), 
                    "处理请求时发生错误：" + e.getMessage()));
                sink.complete();
            }
        });
    }

    /**
     * Process chat request with SSE
     *
     * @param request Chat request
     * @param sseWriter SSE writer
     */
    public void processRequestWithSse(ChatRequest request, SseWriter sseWriter) {
        try {
            // Create or get session
            String sessionId = getOrCreateSession(request);
            request.setSessionId(sessionId);

            // Save user message
            saveUserMessage(request);

            // Get file content if any
            String fileContent = getFileContent(request);
            if (fileContent != null && !fileContent.isEmpty()) {
                request.setMessage(request.getMessage() + "\n\n附件内容：\n" + fileContent);
            }

            // Find the most suitable agent
            BaseAgent selectedAgent = selectAgent(request);
            log.info("Selected agent for SSE: {}", selectedAgent.getAgentName());

            // Process with selected agent using SSE
            selectedAgent.processWithSse(request, sseWriter);

            log.info("SSE processing completed for session: {}", request.getSessionId());

        } catch (Exception e) {
            log.error("Coordinator SSE processing error", e);
            sseWriter.writeErrorEvent("处理请求时发生错误：" + e.getMessage());
        }
    }

    private BaseAgent selectAgent(ChatRequest request) {
        // Sort agents by priority (descending)
        List<BaseAgent> sortedAgents = agents.stream()
                .sorted(Comparator.comparingInt(BaseAgent::getPriority).reversed())
                .collect(Collectors.toList());
        
        // Find first agent that can handle the request
        for (BaseAgent agent : sortedAgents) {
            if (agent.canHandle(request)) {
                return agent;
            }
        }
        
        // Fallback to the last agent (should be ChatAgent)
        return sortedAgents.get(sortedAgents.size() - 1);
    }
    
    private String getOrCreateSession(ChatRequest request) {
        if (request.getSessionId() != null && !request.getSessionId().isEmpty()) {
            // Check if session exists
            ChatSession existingSession = chatSessionMapper.selectById(request.getSessionId());
            if (existingSession != null) {
                return existingSession.getSessionId();
            }
        }
        
        // Create new session
        String sessionId = UUID.randomUUID().toString();
        ChatSession session = new ChatSession();
        session.setSessionId(sessionId);
        session.setTenantId(request.getTenantId());
        session.setUserId(request.getUserId());
        session.setTitle(generateSessionTitle(request.getMessage()));
        session.setStatus("ACTIVE");
        session.setCreateTime(LocalDateTime.now());
        session.setUpdateTime(LocalDateTime.now());
        
        chatSessionMapper.insert(session);
        
        return sessionId;
    }
    
    private String generateSessionTitle(String message) {
        // Generate title from first message
        if (message.length() > 20) {
            return message.substring(0, 20) + "...";
        }
        return message;
    }
    
    private void saveUserMessage(ChatRequest request) {
        ChatMessage message = new ChatMessage();
        message.setSessionId(request.getSessionId());
        message.setMessageId(UUID.randomUUID().toString());
        message.setRole("user");
        message.setContent(request.getMessage());
        message.setMessageType("text");
        message.setCreateTime(LocalDateTime.now());
        
        if (request.getFileIds() != null && !request.getFileIds().isEmpty()) {
            message.setMetadata(String.join(",", request.getFileIds()));
        }
        
        chatMessageMapper.insert(message);
    }
    
    private void saveAssistantMessage(ChatResponse response) {
        ChatMessage message = new ChatMessage();
        message.setSessionId(response.getSessionId());
        message.setMessageId(response.getMessageId());
        message.setRole("assistant");
        message.setContent(response.getContent());
        message.setMessageType(response.getType());
        message.setCreateTime(LocalDateTime.now());
        
        if (response.getData() != null) {
            // Convert data to JSON string
            try {
                message.setMetadata(new com.fasterxml.jackson.databind.ObjectMapper().writeValueAsString(response.getData()));
            } catch (Exception e) {
                log.warn("Failed to serialize response data", e);
            }
        }
        
        chatMessageMapper.insert(message);
    }
    
    private String getFileContent(ChatRequest request) {
        if (request.getFileIds() == null || request.getFileIds().isEmpty()) {
            return null;
        }

        StringBuilder content = new StringBuilder();
        for (String fileId : request.getFileIds()) {
            try {
                // Query by fileId field instead of primary key id
                QueryWrapper<UploadFile> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("file_id", fileId);
                UploadFile file = uploadFileMapper.selectOne(queryWrapper);

                if (file != null && file.getContent() != null) {
                    content.append("文件名: ").append(file.getOriginalName()).append("\n");
                    content.append(file.getContent()).append("\n\n");
                }
            } catch (Exception e) {
                log.warn("Failed to get file content for fileId: {}", fileId, e);
            }
        }

        return content.toString();
    }
}
