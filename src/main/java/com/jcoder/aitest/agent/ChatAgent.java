package com.jcoder.aitest.agent;

import com.jcoder.aitest.dto.ChatRequest;
import com.jcoder.aitest.dto.ChatResponse;
import com.jcoder.aitest.dto.DeepSeekRequest;
import com.jcoder.aitest.service.DeepSeekService;
import com.jcoder.aitest.util.SseWriter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * Chat Agent - Handles general conversation requests
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class ChatAgent implements BaseAgent {
    
    @Autowired
    private DeepSeekService deepSeekService;
    
    private static final String SYSTEM_PROMPT =
        "你是一个友好、专业的AI助手，专门为电商运营人员提供服务。\n" +
        "\n" +
        "你的特点：\n" +
        "1. 友好亲切，善于沟通\n" +
        "2. 了解电商行业背景\n" +
        "3. 能够进行日常对话\n" +
        "4. 在不确定时会诚实地表达不知道\n" +
        "5. 会引导用户使用系统的专业功能\n" +
        "\n" +
        "当用户问及与电商运营无关的问题时，你可以：\n" +
        "1. 简单回应日常问候和闲聊\n" +
        "2. 适当引导用户关注电商运营相关话题\n" +
        "3. 介绍系统的主要功能和能力\n" +
        "\n" +
        "请保持友好、专业的态度与用户交流。";
    
    @Override
    public boolean canHandle(ChatRequest request) {
        // This is the fallback agent, always returns true
        return true;
    }
    
    @Override
    public int getPriority() {
        return 10; // Lowest priority - fallback agent
    }
    
    @Override
    public Flux<ChatResponse> process(ChatRequest request) {
        return Flux.create(sink -> {
            try {
                String messageId = UUID.randomUUID().toString();
                String message = request.getMessage().toLowerCase();

                // 模拟思考过程
                sink.next(ChatResponse.text(request.getSessionId(), messageId,
                    "🤖 正在思考...\n\n", false));

                Thread.sleep(500);

                String response = generateResponse(request.getMessage());

                // 分段发送响应，模拟打字效果 - 发送增量内容
                String[] sentences = response.split("\\n");
                for (int i = 0; i < sentences.length; i++) {
                    if (!sentences[i].trim().isEmpty()) {
                        // 发送增量内容（只发送当前句子）
                        sink.next(ChatResponse.text(request.getSessionId(), messageId,
                            sentences[i] + "\n", false));

                        if (i < sentences.length - 1) {
                            Thread.sleep(300);
                        }
                    }
                }

                // 发送完成标志
                sink.next(ChatResponse.text(request.getSessionId(), messageId, "", true));
                sink.complete();

            } catch (Exception e) {
                log.error("ChatAgent processing failed", e);
                sink.next(ChatResponse.error(request.getSessionId(), UUID.randomUUID().toString(),
                    "对话处理失败，请稍后重试"));
                sink.complete();
            }
        });
    }

    @Override
    public void processWithSse(ChatRequest request, SseWriter sseWriter) {
        try {
            String messageId = UUID.randomUUID().toString();
            log.info("ChatAgent processing request with SSE: {}", request.getMessage());

            // 模拟思考过程
            sseWriter.writeTextEvent(request.getSessionId(), messageId,
                "🤖 正在思考...\n\n", false);

            SseWriter.sleep(500);

            String response = generateResponse(request.getMessage());

            // 分段发送响应，模拟打字效果
            String[] sentences = response.split("\\n");
            for (int i = 0; i < sentences.length; i++) {
                if (!sentences[i].trim().isEmpty()) {
                    // 发送增量内容（只发送当前句子）
                    sseWriter.writeTextEvent(request.getSessionId(), messageId,
                        sentences[i] + "\n", false);

                    if (i < sentences.length - 1) {
                        SseWriter.sleep(300);
                    }
                }
            }

            // 发送完成标志
            sseWriter.writeTextEvent(request.getSessionId(), messageId, "", true);
            log.info("ChatAgent completed processing for session: {}", request.getSessionId());

        } catch (Exception e) {
            log.error("ChatAgent SSE processing failed", e);
            sseWriter.writeErrorEvent("对话处理失败，请稍后重试");
        }
    }
    
    @Override
    public String getAgentName() {
        return "ChatAgent";
    }

    private String generateResponse(String message) {
        String lowerMessage = message.toLowerCase();

        if (lowerMessage.contains("你好") || lowerMessage.contains("hello")) {
            return "您好！👋 欢迎使用智能运营系统！\n\n" +
                   "我是您的AI运营助手，可以帮您：\n" +
                   "📊 **数据查询** - 查询订单、销售、用户等数据\n" +
                   "📈 **业务分析** - 分析运营问题，提供优化建议\n" +
                   "🔍 **运营诊断** - 对店铺、活动进行全面诊断\n" +
                   "💡 **咨询解答** - 解释业务术语和指标含义\n\n" +
                   "请告诉我您需要什么帮助？";
        }

        if (lowerMessage.contains("谢谢") || lowerMessage.contains("感谢")) {
            return "不客气！😊 很高兴能帮助到您！\n\n" +
                   "如果您还有其他问题，随时可以问我：\n" +
                   "• 数据查询和分析\n" +
                   "• 业务优化建议\n" +
                   "• 运营策略制定\n\n" +
                   "我会继续为您提供专业的运营支持！";
        }

        if (lowerMessage.contains("天气")) {
            return "哈哈，我主要专注于电商运营分析，对天气了解不多呢！☀️\n\n" +
                   "不过我可以帮您分析：\n" +
                   "• 天气对销售的影响\n" +
                   "• 季节性商品的销售趋势\n" +
                   "• 不同天气下的用户购买行为\n\n" +
                   "要不我们聊聊您店铺的运营情况？";
        }

        if (lowerMessage.contains("吃饭") || lowerMessage.contains("吃了")) {
            return "我是AI，不需要吃饭，但很关心您的工作效率！😄\n\n" +
                   "工作之余记得好好休息哦！\n\n" +
                   "回到正题，我可以帮您：\n" +
                   "📊 分析店铺数据表现\n" +
                   "💡 制定运营优化策略\n" +
                   "🎯 诊断业务问题\n\n" +
                   "有什么运营问题需要我帮忙分析吗？";
        }

        if (lowerMessage.contains("功能") || lowerMessage.contains("能力") || lowerMessage.contains("帮助")) {
            return "我的核心能力包括：\n\n" +
                   "🔍 **智能数据查询**\n" +
                   "• 自然语言转SQL查询\n" +
                   "• 多维度数据分析\n" +
                   "• 实时数据洞察\n\n" +
                   "📈 **业务分析诊断**\n" +
                   "• 客单价优化分析\n" +
                   "• 转化率提升建议\n" +
                   "• 用户留存策略\n\n" +
                   "🎯 **运营策略制定**\n" +
                   "• 营销活动效果评估\n" +
                   "• 商品运营优化\n" +
                   "• 会员体系诊断\n\n" +
                   "💡 **专业咨询服务**\n" +
                   "• 业务术语解释\n" +
                   "• 指标计算方法\n" +
                   "• 行业最佳实践\n\n" +
                   "您想了解哪个方面的详细信息？";
        }

        // 默认响应
        return "我理解您的问题，但这似乎不在我的专业领域内。🤔\n\n" +
               "我是专业的电商运营AI助手，擅长：\n" +
               "• 📊 数据查询与分析\n" +
               "• 📈 业务问题诊断\n" +
               "• 💡 运营策略建议\n" +
               "• 🎯 营销效果评估\n\n" +
               "比如您可以问我：\n" +
               "\"查询今天的订单数量\"\n" +
               "\"如何提升客单价？\"\n" +
               "\"分析618活动效果\"\n\n" +
               "有什么运营相关的问题需要我帮助吗？";
    }
}
