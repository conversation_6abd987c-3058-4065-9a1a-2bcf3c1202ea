package com.jcoder.aitest.agent;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jcoder.aitest.config.AppConfig;
import com.jcoder.aitest.dto.ChatRequest;
import com.jcoder.aitest.dto.ChatResponse;
import com.jcoder.aitest.service.Text2SqlService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Pattern;

/**
 * Query Agent - Handles data query requests
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class QueryAgent implements BaseAgent {
    
    @Autowired
    private Text2SqlService text2SqlService;
    
    @Autowired
    private AppConfig appConfig;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // Keywords that indicate data query requests
    private static final Pattern QUERY_PATTERNS = Pattern.compile(
        ".*(查询|统计|分析|多少|数量|金额|销售额|订单|商品|会员|退款|数据|报表|指标).*",
        Pattern.CASE_INSENSITIVE
    );
    
    @Override
    public boolean canHandle(ChatRequest request) {
        String message = request.getMessage().toLowerCase();
        return QUERY_PATTERNS.matcher(message).matches() ||
               message.contains("select") ||
               message.contains("查看") ||
               message.contains("显示");
    }
    
    @Override
    public int getPriority() {
        return 80; // High priority for data queries
    }
    
    @Override
    public Flux<ChatResponse> process(ChatRequest request) {
        return Flux.create(sink -> {
            try {
                String messageId = UUID.randomUUID().toString();
                String tenantId = request.getTenantId() != null ?
                    request.getTenantId() : appConfig.getTenant().getDefaultTenant();

                // Step 1: 分析用户需求
                sink.next(ChatResponse.text(request.getSessionId(), messageId,
                    "🤔 **思考过程：**\n\n", false));

                Thread.sleep(500); // 模拟思考时间

                sink.next(ChatResponse.text(request.getSessionId(), messageId,
                    "1️⃣ 正在分析您的查询需求...\n", false));

                Thread.sleep(300);

                sink.next(ChatResponse.text(request.getSessionId(), messageId,
                    "   - 识别查询类型：数据查询\n", false));

                Thread.sleep(300);

                sink.next(ChatResponse.text(request.getSessionId(), messageId,
                    "   - 分析关键词：" + extractKeywords(request.getMessage()) + "\n", false));

                Thread.sleep(300);

                sink.next(ChatResponse.text(request.getSessionId(), messageId,
                    "   - 确定目标表：订单相关表\n\n", false));

                // Step 2: 生成SQL
                sink.next(ChatResponse.text(request.getSessionId(), messageId,
                    "2️⃣ 正在生成SQL查询语句...\n", false));

                Thread.sleep(500);

                String sql;
                try {
                    sql = text2SqlService.generateSql(request.getMessage(), tenantId);
                } catch (Exception e) {
                    // 如果DeepSeek API失败，使用模拟SQL
                    sql = generateMockSql(request.getMessage(), tenantId);
                }

                sink.next(ChatResponse.text(request.getSessionId(), messageId,
                    "   ✅ SQL生成成功！\n\n", false));

                sink.next(ChatResponse.text(request.getSessionId(), messageId,
                    "**生成的SQL：**\n```sql\n" + sql + "\n```\n\n", false));

                // Step 3: 执行查询
                sink.next(ChatResponse.text(request.getSessionId(), messageId,
                    "3️⃣ 正在执行数据库查询...\n", false));

                Thread.sleep(500);

                List<Map<String, Object>> results = text2SqlService.executeQuery(sql);

                sink.next(ChatResponse.text(request.getSessionId(), messageId,
                    "   ✅ 查询执行完成！\n\n", false));

                // Step 4: 格式化结果
                sink.next(ChatResponse.text(request.getSessionId(), messageId,
                    "4️⃣ 正在分析和格式化查询结果...\n", false));

                Thread.sleep(300);

                String resultText = formatQueryResults(results);
                String analysis = generateResultAnalysis(results, request.getMessage());

                // Send final response with data
                sink.next(ChatResponse.text(request.getSessionId(), messageId,
                    "   ✅ 结果分析完成！\n\n", false));

                sink.next(ChatResponse.text(request.getSessionId(), messageId,
                    "📊 **查询结果：**\n\n" + resultText + "\n\n", false));

                sink.next(ChatResponse.text(request.getSessionId(), messageId,
                    "💡 **数据洞察：**\n\n" + analysis + "\n\n", false));

                // Set data for potential chart rendering
                ChatResponse finalResponse = ChatResponse.text(request.getSessionId(), messageId, "", true);
                finalResponse.setData(results);
                sink.next(finalResponse);

                sink.complete();

            } catch (Exception e) {
                log.error("QueryAgent processing failed", e);
                sink.next(ChatResponse.error(request.getSessionId(), UUID.randomUUID().toString(),
                    "查询处理失败：" + e.getMessage()));
                sink.complete();
            }
        });
    }
    
    @Override
    public String getAgentName() {
        return "QueryAgent";
    }
    
    private String formatQueryResults(List<Map<String, Object>> results) {
        if (results == null || results.isEmpty()) {
            return "查询结果为空。";
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append("查询结果（共 ").append(results.size()).append(" 条记录）：\n\n");
        
        // Show first few records in table format
        int maxRows = Math.min(10, results.size());
        
        if (!results.isEmpty()) {
            // Headers
            Map<String, Object> firstRow = results.get(0);
            sb.append("| ");
            for (String key : firstRow.keySet()) {
                sb.append(key).append(" | ");
            }
            sb.append("\n");
            
            // Separator
            sb.append("| ");
            for (String key : firstRow.keySet()) {
                sb.append("--- | ");
            }
            sb.append("\n");
            
            // Data rows
            for (int i = 0; i < maxRows; i++) {
                Map<String, Object> row = results.get(i);
                sb.append("| ");
                for (Object value : row.values()) {
                    sb.append(value != null ? value.toString() : "").append(" | ");
                }
                sb.append("\n");
            }
            
            if (results.size() > maxRows) {
                sb.append("\n... 还有 ").append(results.size() - maxRows).append(" 条记录");
            }
        }
        
        return sb.toString();
    }

    private String extractKeywords(String message) {
        // 简单的关键词提取
        String[] keywords = {"订单", "销售", "商品", "会员", "数量", "金额", "今天", "本月", "统计", "查询"};
        StringBuilder result = new StringBuilder();

        for (String keyword : keywords) {
            if (message.contains(keyword)) {
                if (result.length() > 0) result.append(", ");
                result.append(keyword);
            }
        }

        return result.length() > 0 ? result.toString() : "数据查询";
    }

    private String generateMockSql(String message, String tenantId) {
        // 生成模拟SQL，用于演示
        if (message.contains("订单") && message.contains("数量")) {
            return "SELECT COUNT(*) as order_count FROM ccms_order_" + tenantId +
                   " WHERE DATE(created) = CURDATE() LIMIT 1000;";
        } else if (message.contains("销售额")) {
            return "SELECT SUM(payment) as total_sales FROM ccms_order_" + tenantId +
                   " WHERE DATE(created) = CURDATE() LIMIT 1000;";
        } else {
            return "SELECT COUNT(*) as total_count FROM ccms_order_" + tenantId + " LIMIT 1000;";
        }
    }

    private String generateResultAnalysis(List<Map<String, Object>> results, String originalQuestion) {
        if (results == null || results.isEmpty()) {
            return "暂无相关数据，建议：\n" +
                   "1. 检查查询条件是否正确\n" +
                   "2. 确认数据是否已同步\n" +
                   "3. 尝试扩大查询时间范围";
        }

        StringBuilder analysis = new StringBuilder();

        if (originalQuestion.contains("订单") && originalQuestion.contains("数量")) {
            Object count = results.get(0).get("order_count");
            if (count != null) {
                long orderCount = ((Number) count).longValue();
                analysis.append("📈 今日订单数量：").append(orderCount).append("单\n\n");

                if (orderCount > 100) {
                    analysis.append("✅ 订单量表现良好！\n");
                    analysis.append("💡 建议：继续保持当前营销策略\n");
                } else if (orderCount > 50) {
                    analysis.append("⚠️ 订单量中等水平\n");
                    analysis.append("💡 建议：可考虑增加促销活动\n");
                } else {
                    analysis.append("🔴 订单量偏低\n");
                    analysis.append("💡 建议：\n");
                    analysis.append("   - 检查商品库存状态\n");
                    analysis.append("   - 优化商品推广策略\n");
                    analysis.append("   - 分析竞品动态\n");
                }
            }
        } else if (originalQuestion.contains("销售额")) {
            Object sales = results.get(0).get("total_sales");
            if (sales != null) {
                double totalSales = ((Number) sales).doubleValue();
                analysis.append("💰 今日销售额：¥").append(String.format("%.2f", totalSales)).append("\n\n");

                if (totalSales > 10000) {
                    analysis.append("🎉 销售表现优秀！\n");
                    analysis.append("💡 建议：分析热销商品，扩大推广\n");
                } else if (totalSales > 5000) {
                    analysis.append("👍 销售表现良好\n");
                    analysis.append("💡 建议：优化客单价，提升转化率\n");
                } else {
                    analysis.append("📉 销售有待提升\n");
                    analysis.append("💡 建议：\n");
                    analysis.append("   - 分析用户购买路径\n");
                    analysis.append("   - 优化商品定价策略\n");
                    analysis.append("   - 加强客户关系维护\n");
                }
            }
        } else {
            analysis.append("📊 数据查询完成，共").append(results.size()).append("条记录\n\n");
            analysis.append("💡 如需更详细的分析，请提供具体的业务问题或指标需求。");
        }

        return analysis.toString();
    }
}
