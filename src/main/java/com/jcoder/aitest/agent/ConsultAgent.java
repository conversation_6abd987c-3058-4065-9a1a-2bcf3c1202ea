package com.jcoder.aitest.agent;

import com.jcoder.aitest.dto.ChatRequest;
import com.jcoder.aitest.dto.ChatResponse;
import com.jcoder.aitest.dto.DeepSeekRequest;
import com.jcoder.aitest.service.DeepSeekService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.regex.Pattern;

/**
 * Consult Agent - Handles information consultation requests
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class ConsultAgent implements BaseAgent {
    
    @Autowired
    private DeepSeekService deepSeekService;
    
    // Keywords that indicate consultation requests
    private static final Pattern CONSULT_PATTERNS = Pattern.compile(
        ".*(什么是|如何理解|解释|定义|含义|口径|标准|规则|计算方式|业务含义).*",
        Pattern.CASE_INSENSITIVE
    );
    
    private static final String SYSTEM_PROMPT =
        "你是一个专业的电商业务咨询顾问，精通各种电商业务术语、指标定义和行业标准。\n" +
        "\n" +
        "你的专业领域包括：\n" +
        "1. 电商业务术语和概念解释\n" +
        "2. 数据指标的定义和计算方法\n" +
        "3. 行业标准和最佳实践\n" +
        "4. 平台规则和政策解读\n" +
        "5. 业务流程和操作规范\n" +
        "\n" +
        "回答原则：\n" +
        "1. 准确性：提供准确、权威的信息\n" +
        "2. 清晰性：用简单易懂的语言解释复杂概念\n" +
        "3. 完整性：提供全面的信息，包括相关背景\n" +
        "4. 实用性：结合实际业务场景举例说明\n" +
        "5. 时效性：提供最新的行业标准和规则\n" +
        "\n" +
        "请用专业、准确、易懂的方式回答用户的咨询问题。";
    
    @Override
    public boolean canHandle(ChatRequest request) {
        String message = request.getMessage();
        return CONSULT_PATTERNS.matcher(message).matches();
    }
    
    @Override
    public int getPriority() {
        return 60; // Medium priority for consultation
    }
    
    @Override
    public Flux<ChatResponse> process(ChatRequest request) {
        String messageId = UUID.randomUUID().toString();
        
        // Prepare messages for DeepSeek
        List<DeepSeekRequest.Message> messages = new ArrayList<>();
        messages.add(DeepSeekService.systemMessage(SYSTEM_PROMPT));
        messages.add(DeepSeekService.userMessage(request.getMessage()));
        
        // Stream response from DeepSeek
        return deepSeekService.chatStream(messages)
                .map(content -> ChatResponse.text(request.getSessionId(), messageId, content, false))
                .concatWith(Flux.just(ChatResponse.text(request.getSessionId(), messageId, "", true)))
                .doOnError(error -> log.error("ConsultAgent processing failed", error))
                .onErrorReturn(ChatResponse.error(request.getSessionId(), messageId, 
                    "咨询处理失败，请稍后重试"));
    }
    
    @Override
    public String getAgentName() {
        return "ConsultAgent";
    }
}
