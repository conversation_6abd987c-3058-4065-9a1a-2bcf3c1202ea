package com.jcoder.aitest.agent;

import com.jcoder.aitest.dto.ChatRequest;
import com.jcoder.aitest.dto.ChatResponse;
import com.jcoder.aitest.dto.DeepSeekRequest;
import com.jcoder.aitest.service.DeepSeekService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.regex.Pattern;

/**
 * Diagnosis Agent - Handles theme diagnosis requests
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class DiagnosisAgent implements BaseAgent {
    
    @Autowired
    private DeepSeekService deepSeekService;
    
    // Keywords that indicate diagnosis requests
    private static final Pattern DIAGNOSIS_PATTERNS = Pattern.compile(
        ".*(诊断|检查|评估|体检|健康度|问题|618|双11|大促|活动|会员体系|店铺|运营状况).*",
        Pattern.CASE_INSENSITIVE
    );
    
    private static final String SYSTEM_PROMPT =
        "你是一个专业的电商运营诊断专家，擅长对电商店铺的各个方面进行全面诊断和评估。\n" +
        "\n" +
        "你的诊断能力包括：\n" +
        "1. 店铺运营健康度诊断\n" +
        "2. 营销活动效果诊断（如618、双11等大促活动）\n" +
        "3. 会员体系诊断\n" +
        "4. 商品结构诊断\n" +
        "5. 用户行为诊断\n" +
        "6. 渠道效果诊断\n" +
        "\n" +
        "诊断框架：\n" +
        "1. 现状分析：当前状况的客观描述\n" +
        "2. 问题识别：发现的主要问题和潜在风险\n" +
        "3. 根因分析：问题产生的深层原因\n" +
        "4. 影响评估：问题对业务的影响程度\n" +
        "5. 改进建议：具体的改进措施和优先级\n" +
        "6. 监控指标：需要持续关注的关键指标\n" +
        "\n" +
        "请提供全面、深入、可操作的诊断报告。";
    
    @Override
    public boolean canHandle(ChatRequest request) {
        String message = request.getMessage();
        return DIAGNOSIS_PATTERNS.matcher(message).matches();
    }
    
    @Override
    public int getPriority() {
        return 75; // High priority for diagnosis
    }
    
    @Override
    public Flux<ChatResponse> process(ChatRequest request) {
        String messageId = UUID.randomUUID().toString();
        
        // Prepare messages for DeepSeek
        List<DeepSeekRequest.Message> messages = new ArrayList<>();
        messages.add(DeepSeekService.systemMessage(SYSTEM_PROMPT));
        messages.add(DeepSeekService.userMessage(request.getMessage()));
        
        // Stream response from DeepSeek
        return deepSeekService.chatStream(messages)
                .map(content -> ChatResponse.text(request.getSessionId(), messageId, content, false))
                .concatWith(Flux.just(ChatResponse.text(request.getSessionId(), messageId, "", true)))
                .doOnError(error -> log.error("DiagnosisAgent processing failed", error))
                .onErrorReturn(ChatResponse.error(request.getSessionId(), messageId, 
                    "诊断处理失败，请稍后重试"));
    }
    
    @Override
    public String getAgentName() {
        return "DiagnosisAgent";
    }
}
