package com.jcoder.aitest.agent;

import com.jcoder.aitest.dto.ChatRequest;
import com.jcoder.aitest.dto.ChatResponse;
import com.jcoder.aitest.util.SseWriter;
import reactor.core.publisher.Flux;

/**
 * Base Agent Interface
 *
 * <AUTHOR>
 */
public interface BaseAgent {

    /**
     * Check if this agent can handle the request
     *
     * @param request Chat request
     * @return true if can handle
     */
    boolean canHandle(ChatRequest request);

    /**
     * Get agent priority (higher number = higher priority)
     *
     * @return priority
     */
    int getPriority();

    /**
     * Process the request using Flux (legacy method)
     *
     * @param request Chat request
     * @return Streaming response
     */
    Flux<ChatResponse> process(ChatRequest request);

    /**
     * Process the request using SSE writer (new method)
     *
     * @param request Chat request
     * @param sseWriter SSE writer
     */
    default void processWithSse(ChatRequest request, SseWriter sseWriter) {
        // Default implementation using Flux for backward compatibility
        try {
            process(request)
                .doOnNext(sseWriter::writeEvent)
                .doOnError(error -> sseWriter.writeErrorEvent("处理失败：" + error.getMessage()))
                .blockLast();
        } catch (Exception e) {
            sseWriter.writeErrorEvent("处理失败：" + e.getMessage());
        }
    }

    /**
     * Get agent name
     *
     * @return agent name
     */
    String getAgentName();
}
