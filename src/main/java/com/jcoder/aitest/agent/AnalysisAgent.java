package com.jcoder.aitest.agent;

import com.jcoder.aitest.dto.ChatRequest;
import com.jcoder.aitest.dto.ChatResponse;
import com.jcoder.aitest.dto.DeepSeekRequest;
import com.jcoder.aitest.service.DeepSeekService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.regex.Pattern;

/**
 * Analysis Agent - Handles business analysis requests
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class AnalysisAgent implements BaseAgent {
    
    @Autowired
    private DeepSeekService deepSeekService;
    
    // Keywords that indicate analysis requests
    private static final Pattern ANALYSIS_PATTERNS = Pattern.compile(
        ".*(如何提升|怎么提高|分析|建议|优化|改善|策略|方案|客单价|转化率|复购率|用户价值|营销).*",
        Pattern.CASE_INSENSITIVE
    );
    
    private static final String SYSTEM_PROMPT =
        "你是一个专业的电商运营分析师，具有丰富的电商数据分析和业务优化经验。\n" +
        "\n" +
        "你的职责是：\n" +
        "1. 基于用户提供的业务问题，结合电商行业最佳实践，提供专业的分析和建议\n" +
        "2. 从数据驱动的角度分析问题，提出可执行的优化方案\n" +
        "3. 考虑不同电商平台的特点和用户行为差异\n" +
        "4. 提供具体的KPI指标和监控建议\n" +
        "\n" +
        "分析框架：\n" +
        "1. 问题诊断：明确问题的根本原因\n" +
        "2. 数据分析：需要关注的关键指标\n" +
        "3. 解决方案：具体的优化策略和执行步骤\n" +
        "4. 效果预期：预期的改善效果和时间周期\n" +
        "5. 风险提示：可能的风险和应对措施\n" +
        "\n" +
        "请用专业、清晰、可操作的语言回答用户的问题。";
    
    @Override
    public boolean canHandle(ChatRequest request) {
        String message = request.getMessage();
        return ANALYSIS_PATTERNS.matcher(message).matches();
    }
    
    @Override
    public int getPriority() {
        return 70; // High priority for analysis
    }
    
    @Override
    public Flux<ChatResponse> process(ChatRequest request) {
        return Flux.create(sink -> {
            try {
                String messageId = UUID.randomUUID().toString();

                // Step 1: 问题分析
                sink.next(ChatResponse.text(request.getSessionId(), messageId,
                    "🧠 **业务分析思考过程：**\n\n", false));

                Thread.sleep(500);

                sink.next(ChatResponse.text(request.getSessionId(), messageId,
                    "1️⃣ **问题识别与分类**\n", false));

                Thread.sleep(300);

                String problemType = identifyProblemType(request.getMessage());
                sink.next(ChatResponse.text(request.getSessionId(), messageId,
                    "   - 问题类型：" + problemType + "\n", false));

                Thread.sleep(300);

                String keyFactors = identifyKeyFactors(request.getMessage());
                sink.next(ChatResponse.text(request.getSessionId(), messageId,
                    "   - 关键因素：" + keyFactors + "\n\n", false));

                // Step 2: 数据维度分析
                sink.next(ChatResponse.text(request.getSessionId(), messageId,
                    "2️⃣ **数据维度分析**\n", false));

                Thread.sleep(500);

                String dataDimensions = analyzeDataDimensions(request.getMessage());
                sink.next(ChatResponse.text(request.getSessionId(), messageId,
                    dataDimensions + "\n", false));

                // Step 3: 解决方案制定
                sink.next(ChatResponse.text(request.getSessionId(), messageId,
                    "3️⃣ **解决方案制定**\n", false));

                Thread.sleep(500);

                String solutions = generateSolutions(request.getMessage());
                sink.next(ChatResponse.text(request.getSessionId(), messageId,
                    solutions + "\n", false));

                // Step 4: 实施建议
                sink.next(ChatResponse.text(request.getSessionId(), messageId,
                    "4️⃣ **实施建议与监控**\n", false));

                Thread.sleep(300);

                String implementation = generateImplementationPlan(request.getMessage());
                sink.next(ChatResponse.text(request.getSessionId(), messageId,
                    implementation, true));

                sink.complete();

            } catch (Exception e) {
                log.error("AnalysisAgent processing failed", e);
                sink.next(ChatResponse.error(request.getSessionId(), UUID.randomUUID().toString(),
                    "分析处理失败：" + e.getMessage()));
                sink.complete();
            }
        });
    }
    
    @Override
    public String getAgentName() {
        return "AnalysisAgent";
    }

    private String identifyProblemType(String message) {
        if (message.contains("客单价")) {
            return "客单价优化";
        } else if (message.contains("转化率")) {
            return "转化率提升";
        } else if (message.contains("复购") || message.contains("留存")) {
            return "用户留存与复购";
        } else if (message.contains("流失")) {
            return "用户流失分析";
        } else if (message.contains("营销") || message.contains("推广")) {
            return "营销效果优化";
        } else {
            return "综合运营优化";
        }
    }

    private String identifyKeyFactors(String message) {
        StringBuilder factors = new StringBuilder();

        if (message.contains("客单价")) {
            factors.append("商品定价、购买数量、用户消费能力");
        } else if (message.contains("转化率")) {
            factors.append("流量质量、商品吸引力、购买流程");
        } else if (message.contains("复购")) {
            factors.append("商品质量、服务体验、用户满意度");
        } else {
            factors.append("用户行为、商品表现、市场环境");
        }

        return factors.toString();
    }

    private String analyzeDataDimensions(String message) {
        StringBuilder analysis = new StringBuilder();

        if (message.contains("客单价")) {
            analysis.append("   - 用户维度：新老客户客单价对比\n");
            analysis.append("   - 商品维度：不同价格区间商品销量\n");
            analysis.append("   - 时间维度：客单价变化趋势\n");
            analysis.append("   - 渠道维度：各渠道客单价表现\n");
        } else if (message.contains("转化率")) {
            analysis.append("   - 流量维度：各渠道流量转化情况\n");
            analysis.append("   - 商品维度：商品页面转化率\n");
            analysis.append("   - 用户维度：不同用户群体转化率\n");
            analysis.append("   - 流程维度：购买漏斗各环节转化\n");
        } else {
            analysis.append("   - 需要从用户、商品、时间、渠道等多维度分析\n");
            analysis.append("   - 关注核心指标的变化趋势和影响因素\n");
        }

        return analysis.toString();
    }

    private String generateSolutions(String message) {
        StringBuilder solutions = new StringBuilder();

        if (message.contains("客单价")) {
            solutions.append("   **策略一：商品组合优化**\n");
            solutions.append("   - 推出商品套餐，提高单次购买价值\n");
            solutions.append("   - 优化商品推荐算法，推荐高价值商品\n\n");

            solutions.append("   **策略二：用户分层运营**\n");
            solutions.append("   - 针对高价值用户推送高端商品\n");
            solutions.append("   - 为低客单价用户设计升级路径\n\n");

            solutions.append("   **策略三：促销策略调整**\n");
            solutions.append("   - 设置满减门槛，引导用户增加购买\n");
            solutions.append("   - 限时优惠刺激用户决策\n");

        } else if (message.contains("转化率")) {
            solutions.append("   **策略一：页面体验优化**\n");
            solutions.append("   - 优化商品详情页，提高信息完整度\n");
            solutions.append("   - 简化购买流程，减少操作步骤\n\n");

            solutions.append("   **策略二：信任度建设**\n");
            solutions.append("   - 增加用户评价和晒图\n");
            solutions.append("   - 完善售后保障政策\n\n");

            solutions.append("   **策略三：个性化推荐**\n");
            solutions.append("   - 基于用户行为精准推荐\n");
            solutions.append("   - 实时调整推荐策略\n");

        } else {
            solutions.append("   **综合优化策略：**\n");
            solutions.append("   - 数据驱动决策，持续监控关键指标\n");
            solutions.append("   - 用户体验优先，优化各个触点\n");
            solutions.append("   - 精细化运营，提升整体效率\n");
        }

        return solutions.toString();
    }

    private String generateImplementationPlan(String message) {
        StringBuilder plan = new StringBuilder();

        plan.append("   **实施计划：**\n");
        plan.append("   📅 第1周：数据收集与现状分析\n");
        plan.append("   📅 第2-3周：策略制定与测试\n");
        plan.append("   📅 第4周：全面实施与监控\n\n");

        plan.append("   **关键指标监控：**\n");
        if (message.contains("客单价")) {
            plan.append("   - 平均客单价变化率\n");
            plan.append("   - 商品组合购买率\n");
            plan.append("   - 用户价值分布\n");
        } else if (message.contains("转化率")) {
            plan.append("   - 整体转化率\n");
            plan.append("   - 各环节转化率\n");
            plan.append("   - 用户行为路径\n");
        } else {
            plan.append("   - 核心业务指标\n");
            plan.append("   - 用户满意度\n");
            plan.append("   - ROI表现\n");
        }

        plan.append("\n   **风险提示：**\n");
        plan.append("   ⚠️ 策略调整需要充分测试，避免影响现有业务\n");
        plan.append("   ⚠️ 关注用户反馈，及时调整优化方向\n");
        plan.append("   ⚠️ 建立数据监控机制，确保效果可衡量");

        return plan.toString();
    }
}
