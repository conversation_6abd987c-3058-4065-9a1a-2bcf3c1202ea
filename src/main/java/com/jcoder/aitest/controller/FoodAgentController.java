package com.jcoder.aitest.controller;

import com.jcoder.aitest.entity.User;
import com.jcoder.aitest.service.TicketService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * FoodAgent Subsystem Controller
 */
@Controller
@RequestMapping("/foodagent")
public class FoodAgentController {
    
    @Autowired
    private TicketService ticketService;
    
    /**
     * FoodAgent main page
     */
    @GetMapping("")
    public String foodAgentHome(@RequestParam(required = false) String ticket, Model model) {
        model.addAttribute("ticket", ticket);
        return "foodagent/index";
    }
    
    /**
     * Authenticate with ticket and get user info
     */
    @PostMapping("/api/auth/ticket")
    @ResponseBody
    public Map<String, Object> authenticateWithTicket(@RequestParam String ticket) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // Validate and consume ticket
            User user = ticketService.validateAndConsumeTicket(ticket);
            
            response.put("success", true);
            response.put("user", user);
            response.put("message", "Authentication successful");
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
        }
        
        return response;
    }
    
    /**
     * Get user info from token (without consuming)
     */
    @PostMapping("/api/auth/verify")
    @ResponseBody
    public Map<String, Object> verifyToken(@RequestParam String token) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            User user = ticketService.getUserFromToken(token);
            
            response.put("success", true);
            response.put("user", user);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
        }
        
        return response;
    }
    
    /**
     * FoodAgent dashboard
     */
    @GetMapping("/dashboard")
    public String dashboard() {
        return "foodagent/dashboard";
    }
    
    /**
     * Food management page
     */
    @GetMapping("/foods")
    public String foods() {
        return "foodagent/foods";
    }
    
    /**
     * API: Get food list
     */
    @GetMapping("/api/foods")
    @ResponseBody
    public Map<String, Object> getFoods() {
        Map<String, Object> response = new HashMap<>();
        
        // Mock data for demo
        response.put("success", true);
        response.put("foods", new String[]{
            "Apple", "Banana", "Orange", "Grape", "Strawberry",
            "Chicken", "Beef", "Pork", "Fish", "Shrimp"
        });
        
        return response;
    }
    
    /**
     * API: Add food
     */
    @PostMapping("/api/foods")
    @ResponseBody
    public Map<String, Object> addFood(@RequestParam String name) {
        Map<String, Object> response = new HashMap<>();
        
        // Mock implementation
        response.put("success", true);
        response.put("message", "Food '" + name + "' added successfully");
        
        return response;
    }
}
