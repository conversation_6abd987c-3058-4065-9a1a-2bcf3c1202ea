package com.jcoder.aitest.controller;

import com.jcoder.aitest.entity.User;
import com.jcoder.aitest.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpSession;

/**
 * Demo Controller for testing without Azure AD
 */
@Controller
@RequestMapping("/demo")
public class DemoController {
    
    @Autowired
    private UserService userService;
    
    /**
     * Demo login - creates a test user session
     */
    @GetMapping("/login")
    public String demoLogin(HttpSession session) {
        // Create or get demo user
        User demoUser = userService.findByEmail("<EMAIL>");
        if (demoUser == null) {
            demoUser = userService.createOrUpdateUser(
                "demo-azure-id", 
                "<EMAIL>", 
                "Demo", 
                "Demo User"
            );
        }
        
        // Store user in session for demo purposes
        session.setAttribute("demoUser", demoUser);
        
        return "redirect:/portal";
    }
}
