package com.jcoder.aitest.controller;

import com.jcoder.aitest.entity.Subsystem;
import com.jcoder.aitest.entity.User;
import com.jcoder.aitest.service.SubsystemService;
import com.jcoder.aitest.service.TicketService;
import com.jcoder.aitest.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Portal Controller
 */
@Controller
public class PortalController {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private SubsystemService subsystemService;
    
    @Autowired
    private TicketService ticketService;
    
    /**
     * Home page
     */
    @GetMapping("/")
    public String home() {
        return "index";
    }
    
    /**
     * Login page
     */
    @GetMapping("/login")
    public String login() {
        return "login";
    }
    
    /**
     * Portal main page
     */
    @GetMapping("/portal")
    public String portal(@AuthenticationPrincipal OAuth2User oauth2User, Model model) {
        // Get current user
        String azureId = oauth2User.getAttribute("id");
        User user = userService.findByAzureId(azureId);
        
        // Get all subsystems
        List<Subsystem> subsystems = subsystemService.getAllEnabledSubsystems();
        
        model.addAttribute("user", user);
        model.addAttribute("subsystems", subsystems);
        
        return "portal";
    }
    
    /**
     * Generate ticket for subsystem access
     */
    @PostMapping("/api/ticket/generate")
    @ResponseBody
    public Map<String, Object> generateTicket(
            @AuthenticationPrincipal OAuth2User oauth2User,
            @RequestParam String subsystem) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // Get current user
            String azureId = oauth2User.getAttribute("id");
            User user = userService.findByAzureId(azureId);
            
            if (user == null) {
                response.put("success", false);
                response.put("message", "User not found");
                return response;
            }
            
            // Generate ticket
            String ticket = ticketService.generateTicket(user.getId(), subsystem);
            
            response.put("success", true);
            response.put("ticket", ticket);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
        }
        
        return response;
    }
    
    /**
     * Get current user info
     */
    @GetMapping("/api/user/info")
    @ResponseBody
    public Map<String, Object> getUserInfo(@AuthenticationPrincipal OAuth2User oauth2User) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            String azureId = oauth2User.getAttribute("id");
            User user = userService.findByAzureId(azureId);
            
            if (user != null) {
                response.put("success", true);
                response.put("user", user);
            } else {
                response.put("success", false);
                response.put("message", "User not found");
            }
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
        }
        
        return response;
    }
}
