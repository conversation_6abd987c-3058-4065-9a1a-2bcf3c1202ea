package com.jcoder.aitest.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jcoder.aitest.dto.ChatResponse;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.PrintWriter;

/**
 * SSE Writer Utility
 *
 * <AUTHOR>
 */
@Slf4j
public class SseWriter implements AutoCloseable {
    
    private final PrintWriter writer;
    private final ObjectMapper objectMapper;
    private boolean closed = false;
    
    public SseWriter(PrintWriter writer) {
        this.writer = writer;
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * Write SSE event
     * 
     * @param response Chat response
     */
    public synchronized void writeEvent(ChatResponse response) {
        if (closed) {
            log.warn("Attempted to write to closed SSE stream");
            return;
        }
        
        try {
            String json = objectMapper.writeValueAsString(response);
            writer.write("data: " + json + "\n\n");
            writer.flush();
            
            log.debug("SSE event written: type={}, finished={}", response.getType(), response.isFinished());
            
        } catch (Exception e) {
            log.error("Failed to write SSE event", e);
            writeErrorEvent("写入响应失败");
        }
    }
    
    /**
     * Write text event
     * 
     * @param sessionId Session ID
     * @param messageId Message ID
     * @param content Content
     * @param finished Whether finished
     */
    public void writeTextEvent(String sessionId, String messageId, String content, boolean finished) {
        ChatResponse response = ChatResponse.text(sessionId, messageId, content, finished);
        writeEvent(response);
    }
    
    /**
     * Write error event
     * 
     * @param errorMessage Error message
     */
    public void writeErrorEvent(String errorMessage) {
        if (closed) {
            return;
        }
        
        try {
            ChatResponse errorResponse = ChatResponse.error("", "", errorMessage);
            String json = objectMapper.writeValueAsString(errorResponse);
            writer.write("data: " + json + "\n\n");
            writer.flush();
            
        } catch (Exception e) {
            log.error("Failed to write error event", e);
        }
    }
    
    /**
     * Write chart event
     * 
     * @param sessionId Session ID
     * @param messageId Message ID
     * @param content Content
     * @param data Chart data
     */
    public void writeChartEvent(String sessionId, String messageId, String content, Object data) {
        ChatResponse response = ChatResponse.chart(sessionId, messageId, content, data);
        writeEvent(response);
    }
    
    /**
     * Close the writer
     */
    public synchronized void close() {
        if (!closed) {
            closed = true;
            try {
                writer.close();
            } catch (Exception e) {
                log.error("Failed to close SSE writer", e);
            }
        }
    }
    
    /**
     * Check if writer is closed
     * 
     * @return true if closed
     */
    public boolean isClosed() {
        return closed;
    }
    
    /**
     * Sleep with interruption handling
     * 
     * @param millis Milliseconds to sleep
     */
    public static void sleep(long millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("Sleep interrupted");
        }
    }
}
