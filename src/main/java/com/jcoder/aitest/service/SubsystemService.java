package com.jcoder.aitest.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jcoder.aitest.entity.Subsystem;
import com.jcoder.aitest.mapper.SubsystemMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Subsystem Service
 */
@Service
public class SubsystemService {
    
    @Autowired
    private SubsystemMapper subsystemMapper;
    
    /**
     * Get all enabled subsystems
     */
    public List<Subsystem> getAllEnabledSubsystems() {
        QueryWrapper<Subsystem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1);
        queryWrapper.eq("deleted", 0);
        queryWrapper.orderByAsc("name");
        return subsystemMapper.selectList(queryWrapper);
    }
    
    /**
     * Find subsystem by name
     */
    public Subsystem findByName(String name) {
        QueryWrapper<Subsystem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", name);
        queryWrapper.eq("deleted", 0);
        return subsystemMapper.selectOne(queryWrapper);
    }
    
    /**
     * Create subsystem
     */
    public Subsystem createSubsystem(String name, String displayName, String url, String description) {
        Subsystem subsystem = new Subsystem(name, displayName, url, description);
        subsystemMapper.insert(subsystem);
        return subsystem;
    }
    
    /**
     * Initialize default subsystems
     */
    public void initializeDefaultSubsystems() {
        // Check if foodagent subsystem exists
        Subsystem foodagent = findByName("foodagent");
        if (foodagent == null) {
            createSubsystem("foodagent", "Food Agent", "/foodagent", "Food management system");
        }
    }
}
