package com.jcoder.aitest.service;

import com.jcoder.aitest.config.AppConfig;
import com.jcoder.aitest.entity.UploadFile;
import com.jcoder.aitest.mapper.UploadFileMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * File Service
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class FileService {
    
    @Autowired
    private AppConfig appConfig;
    
    @Autowired
    private UploadFileMapper uploadFileMapper;
    
    /**
     * Upload and process file
     * 
     * @param file Uploaded file
     * @param sessionId Session ID
     * @return Upload file entity
     */
    public UploadFile uploadFile(MultipartFile file, String sessionId) {
        try {
            // Validate file
            validateFile(file);
            
            // Create upload directory
            Path uploadDir = Paths.get(appConfig.getUpload().getPath());
            if (!Files.exists(uploadDir)) {
                Files.createDirectories(uploadDir);
            }
            
            // Generate file info
            String fileId = UUID.randomUUID().toString();
            String originalName = file.getOriginalFilename();
            String fileName = fileId + "_" + originalName;
            String filePath = uploadDir.resolve(fileName).toString();
            
            // Save file
            file.transferTo(new File(filePath));
            
            // Extract content
            String content = extractContent(file, filePath);
            
            // Save to database
            UploadFile uploadFile = new UploadFile();
            uploadFile.setFileId(fileId);
            uploadFile.setSessionId(sessionId);
            uploadFile.setOriginalName(originalName);
            uploadFile.setFileName(fileName);
            uploadFile.setFilePath(filePath);
            uploadFile.setFileType(getFileType(originalName));
            uploadFile.setFileSize(file.getSize());
            uploadFile.setStatus("SUCCESS");
            uploadFile.setContent(content);
            uploadFile.setCreateTime(LocalDateTime.now());
            
            uploadFileMapper.insert(uploadFile);
            
            return uploadFile;
            
        } catch (Exception e) {
            log.error("Failed to upload file: {}", file.getOriginalFilename(), e);
            throw new RuntimeException("File upload failed: " + e.getMessage());
        }
    }
    
    private void validateFile(MultipartFile file) {
        if (file.isEmpty()) {
            throw new IllegalArgumentException("File is empty");
        }
        
        if (file.getSize() > 1024 * 1024) { // 1MB
            throw new IllegalArgumentException("File size exceeds 1MB limit");
        }
        
        String fileName = file.getOriginalFilename();
        if (fileName == null) {
            throw new IllegalArgumentException("File name is null");
        }
        
        String fileType = getFileType(fileName);
        if (!isAllowedFileType(fileType)) {
            throw new IllegalArgumentException("File type not allowed. Only txt, csv, xlsx, xls files are supported");
        }
    }
    
    private String getFileType(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return fileName.substring(lastDotIndex + 1).toLowerCase();
        }
        return "";
    }
    
    private boolean isAllowedFileType(String fileType) {
        return "txt".equals(fileType) || "csv".equals(fileType) || 
               "xlsx".equals(fileType) || "xls".equals(fileType);
    }
    
    private String extractContent(MultipartFile file, String filePath) throws IOException {
        String fileType = getFileType(file.getOriginalFilename());
        
        switch (fileType) {
            case "txt":
                return extractTextContent(filePath);
            case "csv":
                return extractCsvContent(filePath);
            case "xlsx":
            case "xls":
                return extractExcelContent(filePath);
            default:
                return "";
        }
    }
    
    private String extractTextContent(String filePath) throws IOException {
        return new String(Files.readAllBytes(Paths.get(filePath)), StandardCharsets.UTF_8);
    }
    
    private String extractCsvContent(String filePath) throws IOException {
        StringBuilder content = new StringBuilder();
        // 使用JDK 1.8兼容的方式创建带字符集的Reader
        try (InputStreamReader reader = new InputStreamReader(new FileInputStream(filePath), StandardCharsets.UTF_8);
             CSVParser parser = CSVFormat.DEFAULT.withFirstRecordAsHeader().parse(reader)) {

            // Add headers
            content.append("Headers: ").append(String.join(", ", parser.getHeaderNames())).append("\n\n");

            // Add sample data (first 10 rows)
            int rowCount = 0;
            for (CSVRecord record : parser) {
                if (rowCount >= 10) break;
                content.append("Row ").append(rowCount + 1).append(": ");
                for (String value : record) {
                    content.append(value).append(" | ");
                }
                content.append("\n");
                rowCount++;
            }
        }
        return content.toString();
    }
    
    private String extractExcelContent(String filePath) throws IOException {
        StringBuilder content = new StringBuilder();
        
        try (Workbook workbook = new XSSFWorkbook(new FileInputStream(filePath))) {
            Sheet sheet = workbook.getSheetAt(0);
            
            // Add headers
            Row headerRow = sheet.getRow(0);
            if (headerRow != null) {
                content.append("Headers: ");
                for (Cell cell : headerRow) {
                    content.append(getCellValue(cell)).append(" | ");
                }
                content.append("\n\n");
            }
            
            // Add sample data (first 10 rows)
            int rowCount = 0;
            for (Row row : sheet) {
                if (rowCount == 0) { // Skip header
                    rowCount++;
                    continue;
                }
                if (rowCount > 10) break;
                
                content.append("Row ").append(rowCount).append(": ");
                for (Cell cell : row) {
                    content.append(getCellValue(cell)).append(" | ");
                }
                content.append("\n");
                rowCount++;
            }
        }
        
        return content.toString();
    }
    
    private String getCellValue(Cell cell) {
        if (cell == null) return "";
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf(cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }
}
