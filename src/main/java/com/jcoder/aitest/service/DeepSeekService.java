package com.jcoder.aitest.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jcoder.aitest.config.DeepSeekConfig;
import com.jcoder.aitest.dto.DeepSeekRequest;
import com.jcoder.aitest.dto.DeepSeekResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

/**
 * DeepSeek API Service
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class DeepSeekService {
    
    @Autowired
    private DeepSeekConfig deepSeekConfig;
    
    private final WebClient webClient;
    private final ObjectMapper objectMapper;
    
    public DeepSeekService() {
        this.webClient = WebClient.builder()
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(10 * 1024 * 1024))
                .build();
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * Send streaming chat request to DeepSeek
     * 
     * @param messages Chat messages
     * @return Streaming response
     */
    public Flux<String> chatStream(List<DeepSeekRequest.Message> messages) {
        DeepSeekRequest request = new DeepSeekRequest();
        request.setModel(deepSeekConfig.getModel());
        request.setMessages(messages);
        request.setStream(true);
        request.setTemperature(0.7);
        request.setMaxTokens(2000);
        
        return webClient.post()
                .uri(deepSeekConfig.getUrl())
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + deepSeekConfig.getKey())
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .bodyValue(request)
                .retrieve()
                .bodyToFlux(String.class)
                .timeout(Duration.ofMillis(deepSeekConfig.getTimeout()))
                .filter(line -> line.startsWith("data: ") && !line.equals("data: [DONE]"))
                .map(line -> line.substring(6))
                .map(this::parseStreamResponse)
                .filter(content -> content != null && !content.isEmpty())
                .doOnError(error -> log.error("DeepSeek API error: ", error));
    }
    
    /**
     * Send non-streaming chat request to DeepSeek
     * 
     * @param messages Chat messages
     * @return Response
     */
    public DeepSeekResponse chat(List<DeepSeekRequest.Message> messages) {
        DeepSeekRequest request = new DeepSeekRequest();
        request.setModel(deepSeekConfig.getModel());
        request.setMessages(messages);
        request.setStream(false);
        request.setTemperature(0.7);
        request.setMaxTokens(2000);
        
        return webClient.post()
                .uri(deepSeekConfig.getUrl())
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + deepSeekConfig.getKey())
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .bodyValue(request)
                .retrieve()
                .bodyToMono(DeepSeekResponse.class)
                .timeout(Duration.ofMillis(deepSeekConfig.getTimeout()))
                .block();
    }
    
    private String parseStreamResponse(String jsonLine) {
        try {
            DeepSeekResponse response = objectMapper.readValue(jsonLine, DeepSeekResponse.class);
            if (response.getChoices() != null && !response.getChoices().isEmpty()) {
                DeepSeekResponse.Delta delta = response.getChoices().get(0).getDelta();
                if (delta != null && delta.getContent() != null) {
                    return delta.getContent();
                }
            }
        } catch (Exception e) {
            log.warn("Failed to parse stream response: {}", jsonLine, e);
        }
        return null;
    }
    
    /**
     * Create system message
     */
    public static DeepSeekRequest.Message systemMessage(String content) {
        return new DeepSeekRequest.Message("system", content);
    }
    
    /**
     * Create user message
     */
    public static DeepSeekRequest.Message userMessage(String content) {
        return new DeepSeekRequest.Message("user", content);
    }
    
    /**
     * Create assistant message
     */
    public static DeepSeekRequest.Message assistantMessage(String content) {
        return new DeepSeekRequest.Message("assistant", content);
    }
}
