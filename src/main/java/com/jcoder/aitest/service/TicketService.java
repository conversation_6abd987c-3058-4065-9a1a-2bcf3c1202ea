package com.jcoder.aitest.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jcoder.aitest.entity.Ticket;
import com.jcoder.aitest.entity.User;
import com.jcoder.aitest.mapper.TicketMapper;
import com.jcoder.aitest.util.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * Ticket Service
 */
@Service
public class TicketService {
    
    @Autowired
    private TicketMapper ticketMapper;
    
    @Autowired
    private JwtUtil jwtUtil;
    
    @Autowired
    private UserService userService;
    
    /**
     * Generate ticket for user to access subsystem
     */
    public String generateTicket(Long userId, String subsystem) {
        // Generate JWT token
        String jwtToken = jwtUtil.generateTicket(userId, subsystem);
        String ticketId = jwtUtil.getTicketIdFromToken(jwtToken);
        
        // Save ticket to database
        LocalDateTime expiresAt = LocalDateTime.now().plusDays(1); // 24 hours
        Ticket ticket = new Ticket(ticketId, userId, subsystem, expiresAt);
        ticketMapper.insert(ticket);
        
        return jwtToken;
    }
    
    /**
     * Validate and consume ticket
     */
    public User validateAndConsumeTicket(String token) {
        try {
            // Validate JWT token
            if (!jwtUtil.validateToken(token)) {
                throw new RuntimeException("Invalid or expired token");
            }
            
            // Extract information from token
            String ticketId = jwtUtil.getTicketIdFromToken(token);
            Long userId = jwtUtil.getUserIdFromToken(token);
            
            // Find ticket in database
            QueryWrapper<Ticket> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("ticket_id", ticketId);
            queryWrapper.eq("user_id", userId);
            queryWrapper.eq("used", 0);
            Ticket ticket = ticketMapper.selectOne(queryWrapper);
            
            if (ticket == null) {
                throw new RuntimeException("Ticket not found or already used");
            }
            
            // Check if ticket is expired
            if (ticket.getExpiresAt().isBefore(LocalDateTime.now())) {
                throw new RuntimeException("Ticket expired");
            }
            
            // Mark ticket as used
            ticket.setUsed(1);
            ticket.setUsedAt(LocalDateTime.now());
            ticketMapper.updateById(ticket);
            
            // Return user information
            return userService.getUserById(userId);
            
        } catch (Exception e) {
            throw new RuntimeException("Failed to validate ticket: " + e.getMessage());
        }
    }
    
    /**
     * Get user from valid token without consuming it
     */
    public User getUserFromToken(String token) {
        try {
            if (!jwtUtil.validateToken(token)) {
                throw new RuntimeException("Invalid or expired token");
            }
            
            Long userId = jwtUtil.getUserIdFromToken(token);
            return userService.getUserById(userId);
            
        } catch (Exception e) {
            throw new RuntimeException("Failed to get user from token: " + e.getMessage());
        }
    }
}
