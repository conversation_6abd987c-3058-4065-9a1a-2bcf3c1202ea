package com.jcoder.aitest.service;

import com.jcoder.aitest.config.AppConfig;
import com.jcoder.aitest.dto.DeepSeekRequest;
import com.jcoder.aitest.dto.DeepSeekResponse;
import com.jcoder.aitest.mapper.BusinessDataMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Text to SQL Service
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class Text2SqlService {
    
    @Autowired
    private DeepSeekService deepSeekService;
    
    @Autowired
    private BusinessDataMapper businessDataMapper;
    
    @Autowired
    private AppConfig appConfig;
    
    private static final String SYSTEM_PROMPT =
        "你是一个专业的SQL生成助手，专门为电商数据分析生成SQL查询语句。\n" +
        "\n" +
        "数据库表结构说明：\n" +
        "1. 主订单表：ccms_order_{tenantId} - 包含订单基本信息\n" +
        "2. 子订单表：ccms_order_item_{tenantId} - 包含订单商品详情\n" +
        "3. 商品表：ccms_product_{tenantId} - 商品信息\n" +
        "4. 会员表：member_{tenantId} - 会员信息\n" +
        "5. 退款表：ccms_refund_{tenantId} - 退款信息\n" +
        "\n" +
        "重要规则：\n" +
        "1. 所有表名中的{tenantId}必须替换为实际的租户ID\n" +
        "2. 只生成SELECT查询语句，不允许UPDATE、DELETE、INSERT等操作\n" +
        "3. 查询结果限制在1000条以内，使用LIMIT子句\n" +
        "4. 日期字段使用标准格式：YYYY-MM-DD HH:mm:ss\n" +
        "5. 金额字段注意精度，使用decimal类型\n" +
        "\n" +
        "请根据用户的自然语言描述，生成对应的SQL查询语句。\n" +
        "只返回SQL语句，不要包含其他解释文字。";
    
    /**
     * Convert natural language to SQL
     * 
     * @param question Natural language question
     * @param tenantId Tenant ID
     * @return SQL query
     */
    public String generateSql(String question, String tenantId) {
        try {
            // Get database schema
            String schemaInfo = getDatabaseSchema(tenantId);
            
            // Prepare messages
            List<DeepSeekRequest.Message> messages = new ArrayList<>();
            messages.add(DeepSeekService.systemMessage(SYSTEM_PROMPT + "\n\n数据库表结构：\n" + schemaInfo));
            messages.add(DeepSeekService.userMessage("请为以下问题生成SQL查询：" + question));
            
            // Call DeepSeek API
            DeepSeekResponse response = deepSeekService.chat(messages);
            
            if (response != null && response.getChoices() != null && !response.getChoices().isEmpty()) {
                String sql = response.getChoices().get(0).getMessage().getContent();
                
                // Clean and validate SQL
                sql = cleanSql(sql);
                sql = replaceTenantId(sql, tenantId);
                
                // Validate SQL safety
                if (isSafeSql(sql)) {
                    return sql;
                } else {
                    throw new RuntimeException("Generated SQL is not safe for execution");
                }
            }
            
            throw new RuntimeException("Failed to generate SQL from DeepSeek API");
            
        } catch (Exception e) {
            log.error("Failed to generate SQL for question: {}", question, e);
            throw new RuntimeException("SQL generation failed: " + e.getMessage());
        }
    }
    
    /**
     * Execute SQL query
     * 
     * @param sql SQL query
     * @return Query results
     */
    public List<Map<String, Object>> executeQuery(String sql) {
        try {
            // Validate SQL before execution
            if (!isSafeSql(sql)) {
                throw new RuntimeException("SQL query is not safe for execution");
            }
            
            return businessDataMapper.executeDynamicQuery(sql);
            
        } catch (Exception e) {
            log.error("Failed to execute SQL: {}", sql, e);
            throw new RuntimeException("SQL execution failed: " + e.getMessage());
        }
    }
    
    private String getDatabaseSchema(String tenantId) {
        StringBuilder schema = new StringBuilder();
        
        try {
            // Get all tables
            List<Map<String, Object>> tables = businessDataMapper.getAllTables();
            
            for (Map<String, Object> table : tables) {
                String tableName = (String) table.get("TABLE_NAME");
                String tableComment = (String) table.get("TABLE_COMMENT");
                
                // Filter tenant-specific tables
                if (tableName.contains("_" + tenantId) || !tableName.contains("_")) {
                    schema.append("表名: ").append(tableName);
                    if (tableComment != null && !tableComment.isEmpty()) {
                        schema.append(" (").append(tableComment).append(")");
                    }
                    schema.append("\n");
                    
                    // Get table columns
                    List<Map<String, Object>> columns = businessDataMapper.getTableSchema(tableName);
                    for (Map<String, Object> column : columns) {
                        String columnName = (String) column.get("COLUMN_NAME");
                        String dataType = (String) column.get("DATA_TYPE");
                        String columnComment = (String) column.get("COLUMN_COMMENT");
                        
                        schema.append("  - ").append(columnName).append(" (").append(dataType).append(")");
                        if (columnComment != null && !columnComment.isEmpty()) {
                            schema.append(" - ").append(columnComment);
                        }
                        schema.append("\n");
                    }
                    schema.append("\n");
                }
            }
        } catch (Exception e) {
            log.warn("Failed to get database schema", e);
            schema.append("无法获取数据库结构信息");
        }
        
        return schema.toString();
    }
    
    private String cleanSql(String sql) {
        if (sql == null) return "";
        
        // Remove markdown code blocks
        sql = sql.replaceAll("```sql", "").replaceAll("```", "");
        
        // Remove extra whitespace
        sql = sql.trim();
        
        // Ensure it ends with semicolon
        if (!sql.endsWith(";")) {
            sql += ";";
        }
        
        return sql;
    }
    
    private String replaceTenantId(String sql, String tenantId) {
        return sql.replaceAll("\\$\\{tenantId\\}", tenantId)
                  .replaceAll("\\{tenantId\\}", tenantId);
    }
    
    private boolean isSafeSql(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return false;
        }
        
        // Convert to lowercase for checking
        String lowerSql = sql.toLowerCase().trim();
        
        // Must start with SELECT
        if (!lowerSql.startsWith("select")) {
            return false;
        }
        
        // Check for dangerous keywords
        String[] dangerousKeywords = {
            "insert", "update", "delete", "drop", "create", "alter", 
            "truncate", "exec", "execute", "sp_", "xp_", "grant", "revoke"
        };
        
        for (String keyword : dangerousKeywords) {
            if (lowerSql.contains(keyword)) {
                return false;
            }
        }
        
        // Check for LIMIT clause (add if missing)
        if (!lowerSql.contains("limit")) {
            // Add LIMIT to prevent large result sets
            if (sql.endsWith(";")) {
                sql = sql.substring(0, sql.length() - 1) + " LIMIT 1000;";
            } else {
                sql += " LIMIT 1000";
            }
        }
        
        return true;
    }
}
