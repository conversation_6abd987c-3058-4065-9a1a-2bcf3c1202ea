package com.jcoder.aitest.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jcoder.aitest.entity.User;
import com.jcoder.aitest.mapper.UserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * User Service
 */
@Service
public class UserService {
    
    @Autowired
    private UserMapper userMapper;
    
    /**
     * Find user by Azure ID
     */
    public User findByAzureId(String azureId) {
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("azure_id", azureId);
        queryWrapper.eq("deleted", 0);
        return userMapper.selectOne(queryWrapper);
    }
    
    /**
     * Find user by email
     */
    public User findByEmail(String email) {
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("email", email);
        queryWrapper.eq("deleted", 0);
        return userMapper.selectOne(queryWrapper);
    }
    
    /**
     * Create or update user
     */
    public User createOrUpdateUser(String azureId, String email, String name, String displayName) {
        User existingUser = findByAzureId(azureId);
        
        if (existingUser != null) {
            // Update existing user
            existingUser.setEmail(email);
            existingUser.setName(name);
            existingUser.setDisplayName(displayName);
            existingUser.setUpdatedAt(LocalDateTime.now());
            userMapper.updateById(existingUser);
            return existingUser;
        } else {
            // Create new user
            User newUser = new User(azureId, email, name, displayName);
            userMapper.insert(newUser);
            return newUser;
        }
    }
    
    /**
     * Get user by ID
     */
    public User getUserById(Long id) {
        return userMapper.selectById(id);
    }
}
