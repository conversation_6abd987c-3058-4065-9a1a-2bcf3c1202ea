package com.jcoder.aitest.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * Business Data Mapper for dynamic SQL queries
 * 
 * <AUTHOR>
 */
@Mapper
public interface BusinessDataMapper {
    
    /**
     * Execute dynamic SQL query
     * 
     * @param sql SQL statement
     * @return Query results
     */
    @Select("${sql}")
    List<Map<String, Object>> executeDynamicQuery(@Param("sql") String sql);
    
    /**
     * Get table schema information
     * 
     * @param tableName Table name
     * @return Column information
     */
    @Select("SELECT COLUMN_NAME, DATA_TYPE, COLUMN_COMMENT FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = #{tableName} AND TABLE_SCHEMA = DATABASE()")
    List<Map<String, Object>> getTableSchema(@Param("tableName") String tableName);
    
    /**
     * Get all tables in current database
     * 
     * @return Table list
     */
    @Select("SELECT TABLE_NAME, TABLE_COMMENT FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_TYPE = 'BASE TABLE'")
    List<Map<String, Object>> getAllTables();
}
