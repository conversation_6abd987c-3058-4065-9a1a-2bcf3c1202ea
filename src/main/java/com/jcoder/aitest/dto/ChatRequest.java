package com.jcoder.aitest.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * Chat Request DTO
 * 
 * <AUTHOR>
 */
@Data
public class ChatRequest {
    
    private String sessionId;
    
    @NotBlank(message = "Message cannot be empty")
    private String message;
    
    private String tenantId;
    private String userId;
    private List<String> fileIds;
}
