package com.jcoder.aitest.dto;

import lombok.Data;

import java.util.List;

/**
 * DeepSeek API Response DTO
 * 
 * <AUTHOR>
 */
@Data
public class DeepSeekResponse {
    
    private String id;
    private String object;
    private long created;
    private String model;
    private List<Choice> choices;
    private Usage usage;
    
    @Data
    public static class Choice {
        private int index;
        private Message message;
        private Delta delta;
        private String finishReason;
    }
    
    @Data
    public static class Message {
        private String role;
        private String content;
    }
    
    @Data
    public static class Delta {
        private String role;
        private String content;
    }
    
    @Data
    public static class Usage {
        private int promptTokens;
        private int completionTokens;
        private int totalTokens;
    }
}
