package com.jcoder.aitest.dto;

import lombok.Data;

/**
 * Chat Response DTO
 * 
 * <AUTHOR>
 */
@Data
public class ChatResponse {
    
    private String sessionId;
    private String messageId;
    private String content;
    private String type; // text, chart, table, error
    private Object data; // Chart data or table data
    private boolean finished;
    
    public static ChatResponse text(String sessionId, String messageId, String content, boolean finished) {
        ChatResponse response = new ChatResponse();
        response.setSessionId(sessionId);
        response.setMessageId(messageId);
        response.setContent(content);
        response.setType("text");
        response.setFinished(finished);
        return response;
    }
    
    public static ChatResponse chart(String sessionId, String messageId, String content, Object data) {
        ChatResponse response = new ChatResponse();
        response.setSessionId(sessionId);
        response.setMessageId(messageId);
        response.setContent(content);
        response.setType("chart");
        response.setData(data);
        response.setFinished(true);
        return response;
    }
    
    public static ChatResponse error(String sessionId, String messageId, String content) {
        ChatResponse response = new ChatResponse();
        response.setSessionId(sessionId);
        response.setMessageId(messageId);
        response.setContent(content);
        response.setType("error");
        response.setFinished(true);
        return response;
    }
}
