package com.jcoder.aitest.dto;

import lombok.Data;

import java.util.List;

/**
 * DeepSeek API Request DTO
 * 
 * <AUTHOR>
 */
@Data
public class DeepSeekRequest {
    
    private String model;
    private List<Message> messages;
    private boolean stream;
    private double temperature;
    private int maxTokens;
    
    @Data
    public static class Message {
        private String role;
        private String content;
        
        public Message(String role, String content) {
            this.role = role;
            this.content = content;
        }
    }
}
